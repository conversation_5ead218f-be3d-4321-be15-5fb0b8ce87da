# K8s SOP Agent 使用指南

## 🚀 快速开始

### 1. 环境准备
确保已安装依赖并配置环境变量：

```bash
# 激活虚拟环境
source .venv/bin/activate

# 检查环境配置
python quick_test.py
```

### 2. 启动交互模式
```bash
python src/main.py
```

## 💬 基本使用

### 简单问答
```
[default] Your question: What is Kubernetes?
[AI提供详细的Kubernetes介绍]

[default] Your question: Can you explain more about what we just discussed?
[AI基于之前的回答提供更详细的解释]
```

### 多轮对话示例
```
[default] Your question: I'm having issues with my pod deployment
[AI分析pod部署问题]

[default] Your question: What are the common causes for the issue I mentioned?
[AI基于之前提到的问题提供常见原因]

[default] Your question: How can I troubleshoot the problem we discussed?
[AI提供针对性的故障排除步骤]
```

## 🔧 会话管理

### 创建新会话
```
[default] Your question: new session development
Created and switched to new session: development

[development] Your question: I'm working on a dev environment setup
```

### 切换会话
```
[development] Your question: switch session default
Switched to session: default

[default] Your question: What were we discussing before?
```

### 查看所有会话
```
[default] Your question: list sessions
Active sessions:
  default: 5 messages, last activity: 2024-07-14 14:30:15 (current)
  development: 2 messages, last activity: 2024-07-14 14:25:10
```

### 删除会话
```
[default] Your question: delete session development
Deleted session: development
```

### 重置当前会话记忆
```
[default] Your question: reset
Resetting current session memory...
Memory reset successfully.
```

## 📝 会话命令参考

| 命令 | 功能 | 示例 |
|------|------|------|
| `new session [id]` | 创建新会话 | `new session project1` |
| `switch session <id>` | 切换到指定会话 | `switch session project1` |
| `list sessions` | 显示所有活跃会话 | `list sessions` |
| `delete session <id>` | 删除指定会话 | `delete session project1` |
| `reset` | 重置当前会话记忆 | `reset` |
| `exit` 或 `quit` | 退出应用 | `exit` |

## 🎯 最佳实践

### 1. 会话组织
- 为不同项目或环境创建独立会话
- 使用有意义的会话ID（如 `production`, `development`, `troubleshooting`）
- 定期清理不需要的会话

### 2. 提问技巧
- 提供具体的上下文信息
- 包含错误信息或日志片段
- 指定Kubernetes版本和环境详情
- 使用结构化格式（YAML、JSON）提供配置示例

### 3. 记忆管理
- 在长时间对话后使用 `reset` 清理记忆
- 为不同主题创建新会话以避免上下文混乱
- 利用会话隔离功能处理多个并行问题

## 🔍 故障排除

### 常见问题

**Q: 系统无法记住之前的对话**
A: 确保使用同一个会话，检查会话ID是否正确

**Q: 响应速度较慢**
A: 尝试重置会话记忆或创建新会话

**Q: 出现记忆存储错误**
A: 这些错误不影响核心功能，可以继续使用

**Q: LLM调用失败**
A: 检查环境变量配置，确保API密钥和端点正确

### 测试功能
```bash
# 运行快速测试
python quick_test.py

# 运行记忆功能测试
python simple_memory_test.py

# 运行完整演示
python demo_memory.py
```

## 📊 功能特性

### ✅ 已实现
- 多轮对话记忆
- 会话管理和隔离
- 上下文感知回答
- 知识库检索
- 记忆重置功能

### ⚠️ 限制
- 记忆存储可能有技术错误（不影响使用）
- 单用户设计（多用户需要额外配置）
- 长对话可能影响性能

## 🛠️ 高级配置

### 环境变量
```bash
# 必需配置
OPENAI_API_KEY=your_api_key
OPENAI_API_BASE=https://openrouter.ai/api/v1
OPENAI_MODEL_NAME=qwen/qwen3-14b:free

# 可选配置
CREWAI_STORAGE_DIR=./custom_storage
```

### 自定义存储路径
系统会自动在以下位置存储记忆数据：
- macOS: `~/Library/Application Support/CrewAI/`
- Linux: `~/.local/share/CrewAI/`
- Windows: `C:\Users\<USER>\AppData\Local\CrewAI\`

## 📞 支持

如果遇到问题：
1. 查看 `MEMORY_FIX_README.md` 了解技术细节
2. 运行测试脚本验证功能
3. 检查环境变量配置
4. 查看终端输出的错误信息

---

**享受与K8s SOP Agent的智能对话体验！** 🤖✨
