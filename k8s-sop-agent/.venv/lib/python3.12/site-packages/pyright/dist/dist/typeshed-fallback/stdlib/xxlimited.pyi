import sys
from typing import Any, ClassVar, final

class Str(str): ...

@final
class Xxo:
    def demo(self) -> None: ...
    if sys.version_info >= (3, 11) and sys.platform != "win32":
        x_exports: int

def foo(i: int, j: int, /) -> Any: ...
def new() -> Xxo: ...

if sys.version_info >= (3, 10):
    class Error(Exception): ...

else:
    class error(Exception): ...

    class Null:
        __hash__: ClassVar[None]  # type: ignore[assignment]

    def roj(b: Any, /) -> None: ...
