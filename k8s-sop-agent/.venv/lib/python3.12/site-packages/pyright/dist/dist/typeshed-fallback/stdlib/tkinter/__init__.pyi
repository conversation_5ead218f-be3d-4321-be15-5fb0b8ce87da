import _tkinter
import sys
from _typeshed import Incomplete, MaybeNone, StrOrBytesPath
from collections.abc import Callable, Iterable, Mapping, Sequence
from tkinter.constants import *
from tkinter.font import _FontDescription
from types import GenericAlias, TracebackType
from typing import Any, ClassVar, Generic, Literal, NamedTuple, Protocol, TypedDict, TypeVar, overload, type_check_only
from typing_extensions import TypeAlias, TypeVarTuple, Unpack, deprecated

if sys.version_info >= (3, 11):
    from enum import StrEnum
else:
    from enum import Enum

__all__ = [
    "TclError",
    "NO",
    "FALSE",
    "OFF",
    "YES",
    "TRUE",
    "ON",
    "N",
    "S",
    "W",
    "E",
    "NW",
    "SW",
    "NE",
    "SE",
    "NS",
    "EW",
    "NSEW",
    "CENTER",
    "NONE",
    "X",
    "Y",
    "BOTH",
    "LEFT",
    "TOP",
    "RIGHT",
    "BOTTOM",
    "RAISED",
    "<PERSON>UNKE<PERSON>",
    "<PERSON>AT",
    "<PERSON>ID<PERSON>",
    "G<PERSON>O<PERSON>",
    "SOLID",
    "HORI<PERSON>ON<PERSON><PERSON>",
    "VERTICAL",
    "NUMERIC",
    "CHAR",
    "WORD",
    "BASELINE",
    "INSIDE",
    "OUTSIDE",
    "SEL",
    "SEL_FIRST",
    "SEL_LAST",
    "END",
    "INSERT",
    "CURRENT",
    "ANCHOR",
    "ALL",
    "NORMAL",
    "DISABLED",
    "ACTIVE",
    "HIDDEN",
    "CASCADE",
    "CHECKBUTTON",
    "COMMAND",
    "RADIOBUTTON",
    "SEPARATOR",
    "SINGLE",
    "BROWSE",
    "MULTIPLE",
    "EXTENDED",
    "DOTBOX",
    "UNDERLINE",
    "PIESLICE",
    "CHORD",
    "ARC",
    "FIRST",
    "LAST",
    "BUTT",
    "PROJECTING",
    "ROUND",
    "BEVEL",
    "MITER",
    "MOVETO",
    "SCROLL",
    "UNITS",
    "PAGES",
    "TkVersion",
    "TclVersion",
    "READABLE",
    "WRITABLE",
    "EXCEPTION",
    "EventType",
    "Event",
    "NoDefaultRoot",
    "Variable",
    "StringVar",
    "IntVar",
    "DoubleVar",
    "BooleanVar",
    "mainloop",
    "getint",
    "getdouble",
    "getboolean",
    "Misc",
    "CallWrapper",
    "XView",
    "YView",
    "Wm",
    "Tk",
    "Tcl",
    "Pack",
    "Place",
    "Grid",
    "BaseWidget",
    "Widget",
    "Toplevel",
    "Button",
    "Canvas",
    "Checkbutton",
    "Entry",
    "Frame",
    "Label",
    "Listbox",
    "Menu",
    "Menubutton",
    "Message",
    "Radiobutton",
    "Scale",
    "Scrollbar",
    "Text",
    "OptionMenu",
    "Image",
    "PhotoImage",
    "BitmapImage",
    "image_names",
    "image_types",
    "Spinbox",
    "LabelFrame",
    "PanedWindow",
]

# Using anything from tkinter.font in this file means that 'import tkinter'
# seems to also load tkinter.font. That's not how it actually works, but
# unfortunately not much can be done about it. https://github.com/python/typeshed/pull/4346

TclError = _tkinter.TclError
wantobjects: int
TkVersion: float
TclVersion: float
READABLE = _tkinter.READABLE
WRITABLE = _tkinter.WRITABLE
EXCEPTION = _tkinter.EXCEPTION

# Quick guide for figuring out which widget class to choose:
#   - Misc: any widget (don't use BaseWidget because Tk doesn't inherit from BaseWidget)
#   - Widget: anything that is meant to be put into another widget with e.g. pack or grid
#
# Don't trust tkinter's docstrings, because they have been created by copy/pasting from
# Tk's manual pages more than 10 years ago. Use the latest manual pages instead:
#
#    $ sudo apt install tk-doc tcl-doc
#    $ man 3tk label        # tkinter.Label
#    $ man 3tk ttk_label    # tkinter.ttk.Label
#    $ man 3tcl after       # tkinter.Misc.after
#
# You can also read the manual pages online: https://www.tcl.tk/doc/

# Some widgets have an option named -compound that accepts different values
# than the _Compound defined here. Many other options have similar things.
_Anchor: TypeAlias = Literal["nw", "n", "ne", "w", "center", "e", "sw", "s", "se"]  # manual page: Tk_GetAnchor
_ButtonCommand: TypeAlias = str | Callable[[], Any]  # accepts string of tcl code, return value is returned from Button.invoke()
_Compound: TypeAlias = Literal["top", "left", "center", "right", "bottom", "none"]  # -compound in manual page named 'options'
# manual page: Tk_GetCursor
_Cursor: TypeAlias = str | tuple[str] | tuple[str, str] | tuple[str, str, str] | tuple[str, str, str, str]
# example when it's sequence:  entry['invalidcommand'] = [entry.register(print), '%P']
_EntryValidateCommand: TypeAlias = str | list[str] | tuple[str, ...] | Callable[[], bool]
_ImageSpec: TypeAlias = _Image | str  # str can be from e.g. tkinter.image_names()
_Relief: TypeAlias = Literal["raised", "sunken", "flat", "ridge", "solid", "groove"]  # manual page: Tk_GetRelief
_ScreenUnits: TypeAlias = str | float  # Often the right type instead of int. Manual page: Tk_GetPixels
# -xscrollcommand and -yscrollcommand in 'options' manual page
_XYScrollCommand: TypeAlias = str | Callable[[float, float], object]
_TakeFocusValue: TypeAlias = bool | Literal[0, 1, ""] | Callable[[str], bool | None]  # -takefocus in manual page named 'options'

if sys.version_info >= (3, 11):
    @type_check_only
    class _VersionInfoTypeBase(NamedTuple):
        major: int
        minor: int
        micro: int
        releaselevel: str
        serial: int

    class _VersionInfoType(_VersionInfoTypeBase): ...

if sys.version_info >= (3, 11):
    class EventType(StrEnum):
        Activate = "36"
        ButtonPress = "4"
        Button = ButtonPress
        ButtonRelease = "5"
        Circulate = "26"
        CirculateRequest = "27"
        ClientMessage = "33"
        Colormap = "32"
        Configure = "22"
        ConfigureRequest = "23"
        Create = "16"
        Deactivate = "37"
        Destroy = "17"
        Enter = "7"
        Expose = "12"
        FocusIn = "9"
        FocusOut = "10"
        GraphicsExpose = "13"
        Gravity = "24"
        KeyPress = "2"
        Key = "2"
        KeyRelease = "3"
        Keymap = "11"
        Leave = "8"
        Map = "19"
        MapRequest = "20"
        Mapping = "34"
        Motion = "6"
        MouseWheel = "38"
        NoExpose = "14"
        Property = "28"
        Reparent = "21"
        ResizeRequest = "25"
        Selection = "31"
        SelectionClear = "29"
        SelectionRequest = "30"
        Unmap = "18"
        VirtualEvent = "35"
        Visibility = "15"

else:
    class EventType(str, Enum):
        Activate = "36"
        ButtonPress = "4"
        Button = ButtonPress
        ButtonRelease = "5"
        Circulate = "26"
        CirculateRequest = "27"
        ClientMessage = "33"
        Colormap = "32"
        Configure = "22"
        ConfigureRequest = "23"
        Create = "16"
        Deactivate = "37"
        Destroy = "17"
        Enter = "7"
        Expose = "12"
        FocusIn = "9"
        FocusOut = "10"
        GraphicsExpose = "13"
        Gravity = "24"
        KeyPress = "2"
        Key = KeyPress
        KeyRelease = "3"
        Keymap = "11"
        Leave = "8"
        Map = "19"
        MapRequest = "20"
        Mapping = "34"
        Motion = "6"
        MouseWheel = "38"
        NoExpose = "14"
        Property = "28"
        Reparent = "21"
        ResizeRequest = "25"
        Selection = "31"
        SelectionClear = "29"
        SelectionRequest = "30"
        Unmap = "18"
        VirtualEvent = "35"
        Visibility = "15"

_W = TypeVar("_W", bound=Misc)
# Events considered covariant because you should never assign to event.widget.
_W_co = TypeVar("_W_co", covariant=True, bound=Misc, default=Misc)

class Event(Generic[_W_co]):
    serial: int
    num: int
    focus: bool
    height: int
    width: int
    keycode: int
    state: int | str
    time: int
    x: int
    y: int
    x_root: int
    y_root: int
    char: str
    send_event: bool
    keysym: str
    keysym_num: int
    type: EventType
    widget: _W_co
    delta: int
    if sys.version_info >= (3, 14):
        def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...

def NoDefaultRoot() -> None: ...

class Variable:
    def __init__(self, master: Misc | None = None, value=None, name: str | None = None) -> None: ...
    def set(self, value) -> None: ...
    initialize = set
    def get(self): ...
    def trace_add(self, mode: Literal["array", "read", "write", "unset"], callback: Callable[[str, str, str], object]) -> str: ...
    def trace_remove(self, mode: Literal["array", "read", "write", "unset"], cbname: str) -> None: ...
    def trace_info(self) -> list[tuple[tuple[Literal["array", "read", "write", "unset"], ...], str]]: ...
    @deprecated("use trace_add() instead of trace()")
    def trace(self, mode, callback): ...
    @deprecated("use trace_add() instead of trace_variable()")
    def trace_variable(self, mode, callback): ...
    @deprecated("use trace_remove() instead of trace_vdelete()")
    def trace_vdelete(self, mode, cbname) -> None: ...
    @deprecated("use trace_info() instead of trace_vinfo()")
    def trace_vinfo(self): ...
    def __eq__(self, other: object) -> bool: ...
    def __del__(self) -> None: ...
    __hash__: ClassVar[None]  # type: ignore[assignment]

class StringVar(Variable):
    def __init__(self, master: Misc | None = None, value: str | None = None, name: str | None = None) -> None: ...
    def set(self, value: str) -> None: ...
    initialize = set
    def get(self) -> str: ...

class IntVar(Variable):
    def __init__(self, master: Misc | None = None, value: int | None = None, name: str | None = None) -> None: ...
    def set(self, value: int) -> None: ...
    initialize = set
    def get(self) -> int: ...

class DoubleVar(Variable):
    def __init__(self, master: Misc | None = None, value: float | None = None, name: str | None = None) -> None: ...
    def set(self, value: float) -> None: ...
    initialize = set
    def get(self) -> float: ...

class BooleanVar(Variable):
    def __init__(self, master: Misc | None = None, value: bool | None = None, name: str | None = None) -> None: ...
    def set(self, value: bool) -> None: ...
    initialize = set
    def get(self) -> bool: ...

def mainloop(n: int = 0) -> None: ...

getint: Incomplete
getdouble: Incomplete

def getboolean(s): ...

_Ts = TypeVarTuple("_Ts")

class _GridIndexInfo(TypedDict, total=False):
    minsize: _ScreenUnits
    pad: _ScreenUnits
    uniform: str | None
    weight: int

class _BusyInfo(TypedDict):
    cursor: _Cursor

class Misc:
    master: Misc | None
    tk: _tkinter.TkappType
    children: dict[str, Widget]
    def destroy(self) -> None: ...
    def deletecommand(self, name: str) -> None: ...
    def tk_strictMotif(self, boolean=None): ...
    def tk_bisque(self) -> None: ...
    def tk_setPalette(self, *args, **kw) -> None: ...
    def wait_variable(self, name: str | Variable = "PY_VAR") -> None: ...
    waitvar = wait_variable
    def wait_window(self, window: Misc | None = None) -> None: ...
    def wait_visibility(self, window: Misc | None = None) -> None: ...
    def setvar(self, name: str = "PY_VAR", value: str = "1") -> None: ...
    def getvar(self, name: str = "PY_VAR"): ...
    def getint(self, s): ...
    def getdouble(self, s): ...
    def getboolean(self, s): ...
    def focus_set(self) -> None: ...
    focus = focus_set
    def focus_force(self) -> None: ...
    def focus_get(self) -> Misc | None: ...
    def focus_displayof(self) -> Misc | None: ...
    def focus_lastfor(self) -> Misc | None: ...
    def tk_focusFollowsMouse(self) -> None: ...
    def tk_focusNext(self) -> Misc | None: ...
    def tk_focusPrev(self) -> Misc | None: ...
    # .after() can be called without the "func" argument, but it is basically never what you want.
    # It behaves like time.sleep() and freezes the GUI app.
    def after(self, ms: int | Literal["idle"], func: Callable[[Unpack[_Ts]], object], *args: Unpack[_Ts]) -> str: ...
    # after_idle is essentially partialmethod(after, "idle")
    def after_idle(self, func: Callable[[Unpack[_Ts]], object], *args: Unpack[_Ts]) -> str: ...
    def after_cancel(self, id: str) -> None: ...
    if sys.version_info >= (3, 13):
        def after_info(self, id: str | None = None) -> tuple[str, ...]: ...

    def bell(self, displayof: Literal[0] | Misc | None = 0) -> None: ...
    if sys.version_info >= (3, 13):
        # Supports options from `_BusyInfo``
        def tk_busy_cget(self, option: Literal["cursor"]) -> _Cursor: ...
        busy_cget = tk_busy_cget
        def tk_busy_configure(self, cnf: Any = None, **kw: Any) -> Any: ...
        tk_busy_config = tk_busy_configure
        busy_configure = tk_busy_configure
        busy_config = tk_busy_configure
        def tk_busy_current(self, pattern: str | None = None) -> list[Misc]: ...
        busy_current = tk_busy_current
        def tk_busy_forget(self) -> None: ...
        busy_forget = tk_busy_forget
        def tk_busy_hold(self, **kw: Unpack[_BusyInfo]) -> None: ...
        tk_busy = tk_busy_hold
        busy_hold = tk_busy_hold
        busy = tk_busy_hold
        def tk_busy_status(self) -> bool: ...
        busy_status = tk_busy_status

    def clipboard_get(self, *, displayof: Misc = ..., type: str = ...) -> str: ...
    def clipboard_clear(self, *, displayof: Misc = ...) -> None: ...
    def clipboard_append(self, string: str, *, displayof: Misc = ..., format: str = ..., type: str = ...) -> None: ...
    def grab_current(self): ...
    def grab_release(self) -> None: ...
    def grab_set(self) -> None: ...
    def grab_set_global(self) -> None: ...
    def grab_status(self) -> Literal["local", "global"] | None: ...
    def option_add(
        self, pattern, value, priority: int | Literal["widgetDefault", "startupFile", "userDefault", "interactive"] | None = None
    ) -> None: ...
    def option_clear(self) -> None: ...
    def option_get(self, name, className): ...
    def option_readfile(self, fileName, priority=None) -> None: ...
    def selection_clear(self, **kw) -> None: ...
    def selection_get(self, **kw): ...
    def selection_handle(self, command, **kw) -> None: ...
    def selection_own(self, **kw) -> None: ...
    def selection_own_get(self, **kw): ...
    def send(self, interp, cmd, *args): ...
    def lower(self, belowThis=None) -> None: ...
    def tkraise(self, aboveThis=None) -> None: ...
    lift = tkraise
    if sys.version_info >= (3, 11):
        def info_patchlevel(self) -> _VersionInfoType: ...

    def winfo_atom(self, name: str, displayof: Literal[0] | Misc | None = 0) -> int: ...
    def winfo_atomname(self, id: int, displayof: Literal[0] | Misc | None = 0) -> str: ...
    def winfo_cells(self) -> int: ...
    def winfo_children(self) -> list[Widget]: ...  # Widget because it can't be Toplevel or Tk
    def winfo_class(self) -> str: ...
    def winfo_colormapfull(self) -> bool: ...
    def winfo_containing(self, rootX: int, rootY: int, displayof: Literal[0] | Misc | None = 0) -> Misc | None: ...
    def winfo_depth(self) -> int: ...
    def winfo_exists(self) -> bool: ...
    def winfo_fpixels(self, number: _ScreenUnits) -> float: ...
    def winfo_geometry(self) -> str: ...
    def winfo_height(self) -> int: ...
    def winfo_id(self) -> int: ...
    def winfo_interps(self, displayof: Literal[0] | Misc | None = 0) -> tuple[str, ...]: ...
    def winfo_ismapped(self) -> bool: ...
    def winfo_manager(self) -> str: ...
    def winfo_name(self) -> str: ...
    def winfo_parent(self) -> str: ...  # return value needs nametowidget()
    def winfo_pathname(self, id: int, displayof: Literal[0] | Misc | None = 0): ...
    def winfo_pixels(self, number: _ScreenUnits) -> int: ...
    def winfo_pointerx(self) -> int: ...
    def winfo_pointerxy(self) -> tuple[int, int]: ...
    def winfo_pointery(self) -> int: ...
    def winfo_reqheight(self) -> int: ...
    def winfo_reqwidth(self) -> int: ...
    def winfo_rgb(self, color: str) -> tuple[int, int, int]: ...
    def winfo_rootx(self) -> int: ...
    def winfo_rooty(self) -> int: ...
    def winfo_screen(self) -> str: ...
    def winfo_screencells(self) -> int: ...
    def winfo_screendepth(self) -> int: ...
    def winfo_screenheight(self) -> int: ...
    def winfo_screenmmheight(self) -> int: ...
    def winfo_screenmmwidth(self) -> int: ...
    def winfo_screenvisual(self) -> str: ...
    def winfo_screenwidth(self) -> int: ...
    def winfo_server(self) -> str: ...
    def winfo_toplevel(self) -> Tk | Toplevel: ...
    def winfo_viewable(self) -> bool: ...
    def winfo_visual(self) -> str: ...
    def winfo_visualid(self) -> str: ...
    def winfo_visualsavailable(self, includeids: bool = False) -> list[tuple[str, int]]: ...
    def winfo_vrootheight(self) -> int: ...
    def winfo_vrootwidth(self) -> int: ...
    def winfo_vrootx(self) -> int: ...
    def winfo_vrooty(self) -> int: ...
    def winfo_width(self) -> int: ...
    def winfo_x(self) -> int: ...
    def winfo_y(self) -> int: ...
    def update(self) -> None: ...
    def update_idletasks(self) -> None: ...
    @overload
    def bindtags(self, tagList: None = None) -> tuple[str, ...]: ...
    @overload
    def bindtags(self, tagList: list[str] | tuple[str, ...]) -> None: ...
    # bind with isinstance(func, str) doesn't return anything, but all other
    # binds do. The default value of func is not str.
    @overload
    def bind(
        self,
        sequence: str | None = None,
        func: Callable[[Event[Misc]], object] | None = None,
        add: Literal["", "+"] | bool | None = None,
    ) -> str: ...
    @overload
    def bind(self, sequence: str | None, func: str, add: Literal["", "+"] | bool | None = None) -> None: ...
    @overload
    def bind(self, *, func: str, add: Literal["", "+"] | bool | None = None) -> None: ...
    # There's no way to know what type of widget bind_all and bind_class
    # callbacks will get, so those are Misc.
    @overload
    def bind_all(
        self,
        sequence: str | None = None,
        func: Callable[[Event[Misc]], object] | None = None,
        add: Literal["", "+"] | bool | None = None,
    ) -> str: ...
    @overload
    def bind_all(self, sequence: str | None, func: str, add: Literal["", "+"] | bool | None = None) -> None: ...
    @overload
    def bind_all(self, *, func: str, add: Literal["", "+"] | bool | None = None) -> None: ...
    @overload
    def bind_class(
        self,
        className: str,
        sequence: str | None = None,
        func: Callable[[Event[Misc]], object] | None = None,
        add: Literal["", "+"] | bool | None = None,
    ) -> str: ...
    @overload
    def bind_class(self, className: str, sequence: str | None, func: str, add: Literal["", "+"] | bool | None = None) -> None: ...
    @overload
    def bind_class(self, className: str, *, func: str, add: Literal["", "+"] | bool | None = None) -> None: ...
    def unbind(self, sequence: str, funcid: str | None = None) -> None: ...
    def unbind_all(self, sequence: str) -> None: ...
    def unbind_class(self, className: str, sequence: str) -> None: ...
    def mainloop(self, n: int = 0) -> None: ...
    def quit(self) -> None: ...
    @property
    def _windowingsystem(self) -> Literal["win32", "aqua", "x11"]: ...
    def nametowidget(self, name: str | Misc | _tkinter.Tcl_Obj) -> Any: ...
    def register(
        self, func: Callable[..., object], subst: Callable[..., Sequence[Any]] | None = None, needcleanup: int = 1
    ) -> str: ...
    def keys(self) -> list[str]: ...
    @overload
    def pack_propagate(self, flag: bool) -> bool | None: ...
    @overload
    def pack_propagate(self) -> None: ...
    propagate = pack_propagate
    def grid_anchor(self, anchor: _Anchor | None = None) -> None: ...
    anchor = grid_anchor
    @overload
    def grid_bbox(
        self, column: None = None, row: None = None, col2: None = None, row2: None = None
    ) -> tuple[int, int, int, int] | None: ...
    @overload
    def grid_bbox(self, column: int, row: int, col2: None = None, row2: None = None) -> tuple[int, int, int, int] | None: ...
    @overload
    def grid_bbox(self, column: int, row: int, col2: int, row2: int) -> tuple[int, int, int, int] | None: ...
    bbox = grid_bbox
    def grid_columnconfigure(
        self,
        index: int | str | list[int] | tuple[int, ...],
        cnf: _GridIndexInfo = {},
        *,
        minsize: _ScreenUnits = ...,
        pad: _ScreenUnits = ...,
        uniform: str = ...,
        weight: int = ...,
    ) -> _GridIndexInfo | MaybeNone: ...  # can be None but annoying to check
    def grid_rowconfigure(
        self,
        index: int | str | list[int] | tuple[int, ...],
        cnf: _GridIndexInfo = {},
        *,
        minsize: _ScreenUnits = ...,
        pad: _ScreenUnits = ...,
        uniform: str = ...,
        weight: int = ...,
    ) -> _GridIndexInfo | MaybeNone: ...  # can be None but annoying to check
    columnconfigure = grid_columnconfigure
    rowconfigure = grid_rowconfigure
    def grid_location(self, x: _ScreenUnits, y: _ScreenUnits) -> tuple[int, int]: ...
    @overload
    def grid_propagate(self, flag: bool) -> None: ...
    @overload
    def grid_propagate(self) -> bool: ...
    def grid_size(self) -> tuple[int, int]: ...
    size = grid_size
    # Widget because Toplevel or Tk is never a slave
    def pack_slaves(self) -> list[Widget]: ...
    def grid_slaves(self, row: int | None = None, column: int | None = None) -> list[Widget]: ...
    def place_slaves(self) -> list[Widget]: ...
    slaves = pack_slaves
    def event_add(self, virtual: str, *sequences: str) -> None: ...
    def event_delete(self, virtual: str, *sequences: str) -> None: ...
    def event_generate(
        self,
        sequence: str,
        *,
        above: Misc | int = ...,
        borderwidth: _ScreenUnits = ...,
        button: int = ...,
        count: int = ...,
        data: Any = ...,  # anything with usable str() value
        delta: int = ...,
        detail: str = ...,
        focus: bool = ...,
        height: _ScreenUnits = ...,
        keycode: int = ...,
        keysym: str = ...,
        mode: str = ...,
        override: bool = ...,
        place: Literal["PlaceOnTop", "PlaceOnBottom"] = ...,
        root: Misc | int = ...,
        rootx: _ScreenUnits = ...,
        rooty: _ScreenUnits = ...,
        sendevent: bool = ...,
        serial: int = ...,
        state: int | str = ...,
        subwindow: Misc | int = ...,
        time: int = ...,
        warp: bool = ...,
        width: _ScreenUnits = ...,
        when: Literal["now", "tail", "head", "mark"] = ...,
        x: _ScreenUnits = ...,
        y: _ScreenUnits = ...,
    ) -> None: ...
    def event_info(self, virtual: str | None = None) -> tuple[str, ...]: ...
    def image_names(self) -> tuple[str, ...]: ...
    def image_types(self) -> tuple[str, ...]: ...
    # See #4363 and #4891
    def __setitem__(self, key: str, value: Any) -> None: ...
    def __getitem__(self, key: str) -> Any: ...
    def cget(self, key: str) -> Any: ...
    def configure(self, cnf: Any = None) -> Any: ...
    # TODO: config is an alias of configure, but adding that here creates
    # conflict with the type of config in the subclasses. See #13149

class CallWrapper:
    func: Incomplete
    subst: Incomplete
    widget: Incomplete
    def __init__(self, func, subst, widget) -> None: ...
    def __call__(self, *args): ...

class XView:
    @overload
    def xview(self) -> tuple[float, float]: ...
    @overload
    def xview(self, *args): ...
    def xview_moveto(self, fraction: float) -> None: ...
    @overload
    def xview_scroll(self, number: int, what: Literal["units", "pages"]) -> None: ...
    @overload
    def xview_scroll(self, number: _ScreenUnits, what: Literal["pixels"]) -> None: ...

class YView:
    @overload
    def yview(self) -> tuple[float, float]: ...
    @overload
    def yview(self, *args): ...
    def yview_moveto(self, fraction: float) -> None: ...
    @overload
    def yview_scroll(self, number: int, what: Literal["units", "pages"]) -> None: ...
    @overload
    def yview_scroll(self, number: _ScreenUnits, what: Literal["pixels"]) -> None: ...

if sys.platform == "darwin":
    @type_check_only
    class _WmAttributes(TypedDict):
        alpha: float
        fullscreen: bool
        modified: bool
        notify: bool
        titlepath: str
        topmost: bool
        transparent: bool
        type: str  # Present, but not actually used on darwin

elif sys.platform == "win32":
    @type_check_only
    class _WmAttributes(TypedDict):
        alpha: float
        transparentcolor: str
        disabled: bool
        fullscreen: bool
        toolwindow: bool
        topmost: bool

else:
    # X11
    @type_check_only
    class _WmAttributes(TypedDict):
        alpha: float
        topmost: bool
        zoomed: bool
        fullscreen: bool
        type: str

class Wm:
    @overload
    def wm_aspect(self, minNumer: int, minDenom: int, maxNumer: int, maxDenom: int) -> None: ...
    @overload
    def wm_aspect(
        self, minNumer: None = None, minDenom: None = None, maxNumer: None = None, maxDenom: None = None
    ) -> tuple[int, int, int, int] | None: ...
    aspect = wm_aspect
    if sys.version_info >= (3, 13):
        @overload
        def wm_attributes(self, *, return_python_dict: Literal[False] = False) -> tuple[Any, ...]: ...
        @overload
        def wm_attributes(self, *, return_python_dict: Literal[True]) -> _WmAttributes: ...

    else:
        @overload
        def wm_attributes(self) -> tuple[Any, ...]: ...

    @overload
    def wm_attributes(self, option: Literal["-alpha"], /) -> float: ...
    @overload
    def wm_attributes(self, option: Literal["-fullscreen"], /) -> bool: ...
    @overload
    def wm_attributes(self, option: Literal["-topmost"], /) -> bool: ...
    if sys.platform == "darwin":
        @overload
        def wm_attributes(self, option: Literal["-modified"], /) -> bool: ...
        @overload
        def wm_attributes(self, option: Literal["-notify"], /) -> bool: ...
        @overload
        def wm_attributes(self, option: Literal["-titlepath"], /) -> str: ...
        @overload
        def wm_attributes(self, option: Literal["-transparent"], /) -> bool: ...
        @overload
        def wm_attributes(self, option: Literal["-type"], /) -> str: ...
    elif sys.platform == "win32":
        @overload
        def wm_attributes(self, option: Literal["-transparentcolor"], /) -> str: ...
        @overload
        def wm_attributes(self, option: Literal["-disabled"], /) -> bool: ...
        @overload
        def wm_attributes(self, option: Literal["-toolwindow"], /) -> bool: ...
    else:
        # X11
        @overload
        def wm_attributes(self, option: Literal["-zoomed"], /) -> bool: ...
        @overload
        def wm_attributes(self, option: Literal["-type"], /) -> str: ...
    if sys.version_info >= (3, 13):
        @overload
        def wm_attributes(self, option: Literal["alpha"], /) -> float: ...
        @overload
        def wm_attributes(self, option: Literal["fullscreen"], /) -> bool: ...
        @overload
        def wm_attributes(self, option: Literal["topmost"], /) -> bool: ...
        if sys.platform == "darwin":
            @overload
            def wm_attributes(self, option: Literal["modified"], /) -> bool: ...
            @overload
            def wm_attributes(self, option: Literal["notify"], /) -> bool: ...
            @overload
            def wm_attributes(self, option: Literal["titlepath"], /) -> str: ...
            @overload
            def wm_attributes(self, option: Literal["transparent"], /) -> bool: ...
            @overload
            def wm_attributes(self, option: Literal["type"], /) -> str: ...
        elif sys.platform == "win32":
            @overload
            def wm_attributes(self, option: Literal["transparentcolor"], /) -> str: ...
            @overload
            def wm_attributes(self, option: Literal["disabled"], /) -> bool: ...
            @overload
            def wm_attributes(self, option: Literal["toolwindow"], /) -> bool: ...
        else:
            # X11
            @overload
            def wm_attributes(self, option: Literal["zoomed"], /) -> bool: ...
            @overload
            def wm_attributes(self, option: Literal["type"], /) -> str: ...

    @overload
    def wm_attributes(self, option: str, /): ...
    @overload
    def wm_attributes(self, option: Literal["-alpha"], value: float, /) -> Literal[""]: ...
    @overload
    def wm_attributes(self, option: Literal["-fullscreen"], value: bool, /) -> Literal[""]: ...
    @overload
    def wm_attributes(self, option: Literal["-topmost"], value: bool, /) -> Literal[""]: ...
    if sys.platform == "darwin":
        @overload
        def wm_attributes(self, option: Literal["-modified"], value: bool, /) -> Literal[""]: ...
        @overload
        def wm_attributes(self, option: Literal["-notify"], value: bool, /) -> Literal[""]: ...
        @overload
        def wm_attributes(self, option: Literal["-titlepath"], value: str, /) -> Literal[""]: ...
        @overload
        def wm_attributes(self, option: Literal["-transparent"], value: bool, /) -> Literal[""]: ...
    elif sys.platform == "win32":
        @overload
        def wm_attributes(self, option: Literal["-transparentcolor"], value: str, /) -> Literal[""]: ...
        @overload
        def wm_attributes(self, option: Literal["-disabled"], value: bool, /) -> Literal[""]: ...
        @overload
        def wm_attributes(self, option: Literal["-toolwindow"], value: bool, /) -> Literal[""]: ...
    else:
        # X11
        @overload
        def wm_attributes(self, option: Literal["-zoomed"], value: bool, /) -> Literal[""]: ...
        @overload
        def wm_attributes(self, option: Literal["-type"], value: str, /) -> Literal[""]: ...

    @overload
    def wm_attributes(self, option: str, value, /, *__other_option_value_pairs: Any) -> Literal[""]: ...
    if sys.version_info >= (3, 13):
        if sys.platform == "darwin":
            @overload
            def wm_attributes(
                self,
                *,
                alpha: float = ...,
                fullscreen: bool = ...,
                modified: bool = ...,
                notify: bool = ...,
                titlepath: str = ...,
                topmost: bool = ...,
                transparent: bool = ...,
            ) -> None: ...
        elif sys.platform == "win32":
            @overload
            def wm_attributes(
                self,
                *,
                alpha: float = ...,
                transparentcolor: str = ...,
                disabled: bool = ...,
                fullscreen: bool = ...,
                toolwindow: bool = ...,
                topmost: bool = ...,
            ) -> None: ...
        else:
            # X11
            @overload
            def wm_attributes(
                self, *, alpha: float = ..., topmost: bool = ..., zoomed: bool = ..., fullscreen: bool = ..., type: str = ...
            ) -> None: ...

    attributes = wm_attributes
    def wm_client(self, name: str | None = None) -> str: ...
    client = wm_client
    @overload
    def wm_colormapwindows(self) -> list[Misc]: ...
    @overload
    def wm_colormapwindows(self, wlist: list[Misc] | tuple[Misc, ...], /) -> None: ...
    @overload
    def wm_colormapwindows(self, first_wlist_item: Misc, /, *other_wlist_items: Misc) -> None: ...
    colormapwindows = wm_colormapwindows
    def wm_command(self, value: str | None = None) -> str: ...
    command = wm_command
    # Some of these always return empty string, but return type is set to None to prevent accidentally using it
    def wm_deiconify(self) -> None: ...
    deiconify = wm_deiconify
    def wm_focusmodel(self, model: Literal["active", "passive"] | None = None) -> Literal["active", "passive", ""]: ...
    focusmodel = wm_focusmodel
    def wm_forget(self, window: Wm) -> None: ...
    forget = wm_forget
    def wm_frame(self) -> str: ...
    frame = wm_frame
    @overload
    def wm_geometry(self, newGeometry: None = None) -> str: ...
    @overload
    def wm_geometry(self, newGeometry: str) -> None: ...
    geometry = wm_geometry
    def wm_grid(self, baseWidth=None, baseHeight=None, widthInc=None, heightInc=None): ...
    grid = wm_grid
    def wm_group(self, pathName=None): ...
    group = wm_group
    def wm_iconbitmap(self, bitmap=None, default=None): ...
    iconbitmap = wm_iconbitmap
    def wm_iconify(self) -> None: ...
    iconify = wm_iconify
    def wm_iconmask(self, bitmap=None): ...
    iconmask = wm_iconmask
    def wm_iconname(self, newName=None) -> str: ...
    iconname = wm_iconname
    def wm_iconphoto(self, default: bool, image1: _PhotoImageLike | str, /, *args: _PhotoImageLike | str) -> None: ...
    iconphoto = wm_iconphoto
    def wm_iconposition(self, x: int | None = None, y: int | None = None) -> tuple[int, int] | None: ...
    iconposition = wm_iconposition
    def wm_iconwindow(self, pathName=None): ...
    iconwindow = wm_iconwindow
    def wm_manage(self, widget) -> None: ...
    manage = wm_manage
    @overload
    def wm_maxsize(self, width: None = None, height: None = None) -> tuple[int, int]: ...
    @overload
    def wm_maxsize(self, width: int, height: int) -> None: ...
    maxsize = wm_maxsize
    @overload
    def wm_minsize(self, width: None = None, height: None = None) -> tuple[int, int]: ...
    @overload
    def wm_minsize(self, width: int, height: int) -> None: ...
    minsize = wm_minsize
    @overload
    def wm_overrideredirect(self, boolean: None = None) -> bool | None: ...  # returns True or None
    @overload
    def wm_overrideredirect(self, boolean: bool) -> None: ...
    overrideredirect = wm_overrideredirect
    def wm_positionfrom(self, who: Literal["program", "user"] | None = None) -> Literal["", "program", "user"]: ...
    positionfrom = wm_positionfrom
    @overload
    def wm_protocol(self, name: str, func: Callable[[], object] | str) -> None: ...
    @overload
    def wm_protocol(self, name: str, func: None = None) -> str: ...
    @overload
    def wm_protocol(self, name: None = None, func: None = None) -> tuple[str, ...]: ...
    protocol = wm_protocol
    @overload
    def wm_resizable(self, width: None = None, height: None = None) -> tuple[bool, bool]: ...
    @overload
    def wm_resizable(self, width: bool, height: bool) -> None: ...
    resizable = wm_resizable
    def wm_sizefrom(self, who: Literal["program", "user"] | None = None) -> Literal["", "program", "user"]: ...
    sizefrom = wm_sizefrom
    @overload
    def wm_state(self, newstate: None = None) -> str: ...
    @overload
    def wm_state(self, newstate: str) -> None: ...
    state = wm_state
    @overload
    def wm_title(self, string: None = None) -> str: ...
    @overload
    def wm_title(self, string: str) -> None: ...
    title = wm_title
    @overload
    def wm_transient(self, master: None = None) -> _tkinter.Tcl_Obj: ...
    @overload
    def wm_transient(self, master: Wm | _tkinter.Tcl_Obj) -> None: ...
    transient = wm_transient
    def wm_withdraw(self) -> None: ...
    withdraw = wm_withdraw

class Tk(Misc, Wm):
    master: None
    def __init__(
        # Make sure to keep in sync with other functions that use the same
        # args.
        # use `git grep screenName` to find them
        self,
        screenName: str | None = None,
        baseName: str | None = None,
        className: str = "Tk",
        useTk: bool = True,
        sync: bool = False,
        use: str | None = None,
    ) -> None: ...
    # Keep this in sync with ttktheme.ThemedTk. See issue #13858
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        menu: Menu = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        takefocus: _TakeFocusValue = ...,
        width: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def destroy(self) -> None: ...
    def readprofile(self, baseName: str, className: str) -> None: ...
    report_callback_exception: Callable[[type[BaseException], BaseException, TracebackType | None], object]
    # Tk has __getattr__ so that tk_instance.foo falls back to tk_instance.tk.foo
    # Please keep in sync with _tkinter.TkappType.
    # Some methods are intentionally missing because they are inherited from Misc instead.
    def adderrorinfo(self, msg, /): ...
    def call(self, command: Any, /, *args: Any) -> Any: ...
    def createcommand(self, name, func, /): ...
    if sys.platform != "win32":
        def createfilehandler(self, file, mask, func, /): ...
        def deletefilehandler(self, file, /): ...

    def createtimerhandler(self, milliseconds, func, /): ...
    def dooneevent(self, flags: int = ..., /): ...
    def eval(self, script: str, /) -> str: ...
    def evalfile(self, fileName, /): ...
    def exprboolean(self, s, /): ...
    def exprdouble(self, s, /): ...
    def exprlong(self, s, /): ...
    def exprstring(self, s, /): ...
    def globalgetvar(self, *args, **kwargs): ...
    def globalsetvar(self, *args, **kwargs): ...
    def globalunsetvar(self, *args, **kwargs): ...
    def interpaddr(self) -> int: ...
    def loadtk(self) -> None: ...
    def record(self, script, /): ...
    if sys.version_info < (3, 11):
        def split(self, arg, /): ...

    def splitlist(self, arg, /): ...
    def unsetvar(self, *args, **kwargs): ...
    def wantobjects(self, *args, **kwargs): ...
    def willdispatch(self): ...

def Tcl(screenName: str | None = None, baseName: str | None = None, className: str = "Tk", useTk: bool = False) -> Tk: ...

_InMiscTotal = TypedDict("_InMiscTotal", {"in": Misc})
_InMiscNonTotal = TypedDict("_InMiscNonTotal", {"in": Misc}, total=False)

class _PackInfo(_InMiscTotal):
    # 'before' and 'after' never appear in _PackInfo
    anchor: _Anchor
    expand: bool
    fill: Literal["none", "x", "y", "both"]
    side: Literal["left", "right", "top", "bottom"]
    # Paddings come out as int or tuple of int, even though any _ScreenUnits
    # can be specified in pack().
    ipadx: int
    ipady: int
    padx: int | tuple[int, int]
    pady: int | tuple[int, int]

class Pack:
    # _PackInfo is not the valid type for cnf because pad stuff accepts any
    # _ScreenUnits instead of int only. I didn't bother to create another
    # TypedDict for cnf because it appears to be a legacy thing that was
    # replaced by **kwargs.
    def pack_configure(
        self,
        cnf: Mapping[str, Any] | None = {},
        *,
        after: Misc = ...,
        anchor: _Anchor = ...,
        before: Misc = ...,
        expand: bool | Literal[0, 1] = 0,
        fill: Literal["none", "x", "y", "both"] = ...,
        side: Literal["left", "right", "top", "bottom"] = ...,
        ipadx: _ScreenUnits = ...,
        ipady: _ScreenUnits = ...,
        padx: _ScreenUnits | tuple[_ScreenUnits, _ScreenUnits] = ...,
        pady: _ScreenUnits | tuple[_ScreenUnits, _ScreenUnits] = ...,
        in_: Misc = ...,
        **kw: Any,  # allow keyword argument named 'in', see #4836
    ) -> None: ...
    def pack_forget(self) -> None: ...
    def pack_info(self) -> _PackInfo: ...  # errors if widget hasn't been packed
    pack = pack_configure
    forget = pack_forget
    propagate = Misc.pack_propagate

class _PlaceInfo(_InMiscNonTotal):  # empty dict if widget hasn't been placed
    anchor: _Anchor
    bordermode: Literal["inside", "outside", "ignore"]
    width: str  # can be int()ed (even after e.g. widget.place(height='2.3c') or similar)
    height: str  # can be int()ed
    x: str  # can be int()ed
    y: str  # can be int()ed
    relheight: str  # can be float()ed if not empty string
    relwidth: str  # can be float()ed if not empty string
    relx: str  # can be float()ed if not empty string
    rely: str  # can be float()ed if not empty string

class Place:
    def place_configure(
        self,
        cnf: Mapping[str, Any] | None = {},
        *,
        anchor: _Anchor = ...,
        bordermode: Literal["inside", "outside", "ignore"] = ...,
        width: _ScreenUnits = ...,
        height: _ScreenUnits = ...,
        x: _ScreenUnits = ...,
        y: _ScreenUnits = ...,
        # str allowed for compatibility with place_info()
        relheight: str | float = ...,
        relwidth: str | float = ...,
        relx: str | float = ...,
        rely: str | float = ...,
        in_: Misc = ...,
        **kw: Any,  # allow keyword argument named 'in', see #4836
    ) -> None: ...
    def place_forget(self) -> None: ...
    def place_info(self) -> _PlaceInfo: ...
    place = place_configure
    info = place_info

class _GridInfo(_InMiscNonTotal):  # empty dict if widget hasn't been gridded
    column: int
    columnspan: int
    row: int
    rowspan: int
    ipadx: int
    ipady: int
    padx: int | tuple[int, int]
    pady: int | tuple[int, int]
    sticky: str  # consists of letters 'n', 's', 'w', 'e', no repeats, may be empty

class Grid:
    def grid_configure(
        self,
        cnf: Mapping[str, Any] | None = {},
        *,
        column: int = ...,
        columnspan: int = ...,
        row: int = ...,
        rowspan: int = ...,
        ipadx: _ScreenUnits = ...,
        ipady: _ScreenUnits = ...,
        padx: _ScreenUnits | tuple[_ScreenUnits, _ScreenUnits] = ...,
        pady: _ScreenUnits | tuple[_ScreenUnits, _ScreenUnits] = ...,
        sticky: str = ...,  # consists of letters 'n', 's', 'w', 'e', may contain repeats, may be empty
        in_: Misc = ...,
        **kw: Any,  # allow keyword argument named 'in', see #4836
    ) -> None: ...
    def grid_forget(self) -> None: ...
    def grid_remove(self) -> None: ...
    def grid_info(self) -> _GridInfo: ...
    grid = grid_configure
    location = Misc.grid_location
    size = Misc.grid_size

class BaseWidget(Misc):
    master: Misc
    widgetName: Incomplete
    def __init__(self, master, widgetName, cnf={}, kw={}, extra=()) -> None: ...
    def destroy(self) -> None: ...

# This class represents any widget except Toplevel or Tk.
class Widget(BaseWidget, Pack, Place, Grid):
    # Allow bind callbacks to take e.g. Event[Label] instead of Event[Misc].
    # Tk and Toplevel get notified for their child widgets' events, but other
    # widgets don't.
    @overload
    def bind(
        self: _W,
        sequence: str | None = None,
        func: Callable[[Event[_W]], object] | None = None,
        add: Literal["", "+"] | bool | None = None,
    ) -> str: ...
    @overload
    def bind(self, sequence: str | None, func: str, add: Literal["", "+"] | bool | None = None) -> None: ...
    @overload
    def bind(self, *, func: str, add: Literal["", "+"] | bool | None = None) -> None: ...

class Toplevel(BaseWidget, Wm):
    # Toplevel and Tk have the same options because they correspond to the same
    # Tcl/Tk toplevel widget. For some reason, config and configure must be
    # copy/pasted here instead of aliasing as 'config = Tk.config'.
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        background: str = ...,
        bd: _ScreenUnits = 0,
        bg: str = ...,
        border: _ScreenUnits = 0,
        borderwidth: _ScreenUnits = 0,
        class_: str = "Toplevel",
        colormap: Literal["new", ""] | Misc = "",
        container: bool = False,
        cursor: _Cursor = "",
        height: _ScreenUnits = 0,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = 0,
        menu: Menu = ...,
        name: str = ...,
        padx: _ScreenUnits = 0,
        pady: _ScreenUnits = 0,
        relief: _Relief = "flat",
        screen: str = "",  # can't be changed after creating widget
        takefocus: _TakeFocusValue = 0,
        use: int = ...,
        visual: str | tuple[str, int] = "",
        width: _ScreenUnits = 0,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        menu: Menu = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        takefocus: _TakeFocusValue = ...,
        width: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure

class Button(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        activebackground: str = ...,
        activeforeground: str = ...,
        anchor: _Anchor = "center",
        background: str = ...,
        bd: _ScreenUnits = ...,  # same as borderwidth
        bg: str = ...,  # same as background
        bitmap: str = "",
        border: _ScreenUnits = ...,  # same as borderwidth
        borderwidth: _ScreenUnits = ...,
        command: _ButtonCommand = "",
        compound: _Compound = "none",
        cursor: _Cursor = "",
        default: Literal["normal", "active", "disabled"] = "disabled",
        disabledforeground: str = ...,
        fg: str = ...,  # same as foreground
        font: _FontDescription = "TkDefaultFont",
        foreground: str = ...,
        # width and height must be int for buttons containing just text, but
        # ints are also valid _ScreenUnits
        height: _ScreenUnits = 0,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = 1,
        image: _ImageSpec = "",
        justify: Literal["left", "center", "right"] = "center",
        name: str = ...,
        overrelief: _Relief | Literal[""] = "",
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        repeatdelay: int = ...,
        repeatinterval: int = ...,
        state: Literal["normal", "active", "disabled"] = "normal",
        takefocus: _TakeFocusValue = "",
        text: float | str = "",
        # We allow the textvariable to be any Variable, not necessarily
        # StringVar. This is useful for e.g. a button that displays the value
        # of an IntVar.
        textvariable: Variable = ...,
        underline: int = -1,
        width: _ScreenUnits = 0,
        wraplength: _ScreenUnits = 0,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        activebackground: str = ...,
        activeforeground: str = ...,
        anchor: _Anchor = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        bitmap: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        command: _ButtonCommand = ...,
        compound: _Compound = ...,
        cursor: _Cursor = ...,
        default: Literal["normal", "active", "disabled"] = ...,
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        image: _ImageSpec = ...,
        justify: Literal["left", "center", "right"] = ...,
        overrelief: _Relief | Literal[""] = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        repeatdelay: int = ...,
        repeatinterval: int = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        takefocus: _TakeFocusValue = ...,
        text: float | str = ...,
        textvariable: Variable = ...,
        underline: int = ...,
        width: _ScreenUnits = ...,
        wraplength: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def flash(self) -> None: ...
    def invoke(self) -> Any: ...

class Canvas(Widget, XView, YView):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        background: str = ...,
        bd: _ScreenUnits = 0,
        bg: str = ...,
        border: _ScreenUnits = 0,
        borderwidth: _ScreenUnits = 0,
        closeenough: float = 1.0,
        confine: bool = True,
        cursor: _Cursor = "",
        # canvas manual page has a section named COORDINATES, and the first
        # part of it describes _ScreenUnits.
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        insertbackground: str = ...,
        insertborderwidth: _ScreenUnits = 0,
        insertofftime: int = 300,
        insertontime: int = 600,
        insertwidth: _ScreenUnits = 2,
        name: str = ...,
        offset=...,  # undocumented
        relief: _Relief = "flat",
        # Setting scrollregion to None doesn't reset it back to empty,
        # but setting it to () does.
        scrollregion: tuple[_ScreenUnits, _ScreenUnits, _ScreenUnits, _ScreenUnits] | tuple[()] = (),
        selectbackground: str = ...,
        selectborderwidth: _ScreenUnits = 1,
        selectforeground: str = ...,
        # man page says that state can be 'hidden', but it can't
        state: Literal["normal", "disabled"] = "normal",
        takefocus: _TakeFocusValue = "",
        width: _ScreenUnits = ...,
        xscrollcommand: _XYScrollCommand = "",
        xscrollincrement: _ScreenUnits = 0,
        yscrollcommand: _XYScrollCommand = "",
        yscrollincrement: _ScreenUnits = 0,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        closeenough: float = ...,
        confine: bool = ...,
        cursor: _Cursor = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        insertbackground: str = ...,
        insertborderwidth: _ScreenUnits = ...,
        insertofftime: int = ...,
        insertontime: int = ...,
        insertwidth: _ScreenUnits = ...,
        offset=...,  # undocumented
        relief: _Relief = ...,
        scrollregion: tuple[_ScreenUnits, _ScreenUnits, _ScreenUnits, _ScreenUnits] | tuple[()] = ...,
        selectbackground: str = ...,
        selectborderwidth: _ScreenUnits = ...,
        selectforeground: str = ...,
        state: Literal["normal", "disabled"] = ...,
        takefocus: _TakeFocusValue = ...,
        width: _ScreenUnits = ...,
        xscrollcommand: _XYScrollCommand = ...,
        xscrollincrement: _ScreenUnits = ...,
        yscrollcommand: _XYScrollCommand = ...,
        yscrollincrement: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def addtag(self, *args): ...  # internal method
    def addtag_above(self, newtag: str, tagOrId: str | int) -> None: ...
    def addtag_all(self, newtag: str) -> None: ...
    def addtag_below(self, newtag: str, tagOrId: str | int) -> None: ...
    def addtag_closest(
        self, newtag: str, x: _ScreenUnits, y: _ScreenUnits, halo: _ScreenUnits | None = None, start: str | int | None = None
    ) -> None: ...
    def addtag_enclosed(self, newtag: str, x1: _ScreenUnits, y1: _ScreenUnits, x2: _ScreenUnits, y2: _ScreenUnits) -> None: ...
    def addtag_overlapping(self, newtag: str, x1: _ScreenUnits, y1: _ScreenUnits, x2: _ScreenUnits, y2: _ScreenUnits) -> None: ...
    def addtag_withtag(self, newtag: str, tagOrId: str | int) -> None: ...
    def find(self, *args): ...  # internal method
    def find_above(self, tagOrId: str | int) -> tuple[int, ...]: ...
    def find_all(self) -> tuple[int, ...]: ...
    def find_below(self, tagOrId: str | int) -> tuple[int, ...]: ...
    def find_closest(
        self, x: _ScreenUnits, y: _ScreenUnits, halo: _ScreenUnits | None = None, start: str | int | None = None
    ) -> tuple[int, ...]: ...
    def find_enclosed(self, x1: _ScreenUnits, y1: _ScreenUnits, x2: _ScreenUnits, y2: _ScreenUnits) -> tuple[int, ...]: ...
    def find_overlapping(self, x1: _ScreenUnits, y1: _ScreenUnits, x2: _ScreenUnits, y2: float) -> tuple[int, ...]: ...
    def find_withtag(self, tagOrId: str | int) -> tuple[int, ...]: ...
    # Incompatible with Misc.bbox(), tkinter violates LSP
    def bbox(self, *args: str | int) -> tuple[int, int, int, int]: ...  # type: ignore[override]
    @overload
    def tag_bind(
        self,
        tagOrId: str | int,
        sequence: str | None = None,
        func: Callable[[Event[Canvas]], object] | None = None,
        add: Literal["", "+"] | bool | None = None,
    ) -> str: ...
    @overload
    def tag_bind(
        self, tagOrId: str | int, sequence: str | None, func: str, add: Literal["", "+"] | bool | None = None
    ) -> None: ...
    @overload
    def tag_bind(self, tagOrId: str | int, *, func: str, add: Literal["", "+"] | bool | None = None) -> None: ...
    def tag_unbind(self, tagOrId: str | int, sequence: str, funcid: str | None = None) -> None: ...
    def canvasx(self, screenx, gridspacing=None): ...
    def canvasy(self, screeny, gridspacing=None): ...
    @overload
    def coords(self, tagOrId: str | int, /) -> list[float]: ...
    @overload
    def coords(self, tagOrId: str | int, args: list[int] | list[float] | tuple[float, ...], /) -> None: ...
    @overload
    def coords(self, tagOrId: str | int, x1: float, y1: float, /, *args: float) -> None: ...
    # create_foo() methods accept coords as a list or tuple, or as separate arguments.
    # Lists and tuples can be flat as in [1, 2, 3, 4], or nested as in [(1, 2), (3, 4)].
    # Keyword arguments should be the same in all overloads of each method.
    def create_arc(self, *args, **kw) -> int: ...
    def create_bitmap(self, *args, **kw) -> int: ...
    def create_image(self, *args, **kw) -> int: ...
    @overload
    def create_line(
        self,
        x0: float,
        y0: float,
        x1: float,
        y1: float,
        /,
        *,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        arrow: Literal["first", "last", "both"] = ...,
        arrowshape: tuple[float, float, float] = ...,
        capstyle: Literal["round", "projecting", "butt"] = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        joinstyle: Literal["round", "bevel", "miter"] = ...,
        offset: _ScreenUnits = ...,
        smooth: bool = ...,
        splinesteps: float = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_line(
        self,
        xy_pair_0: tuple[float, float],
        xy_pair_1: tuple[float, float],
        /,
        *,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        arrow: Literal["first", "last", "both"] = ...,
        arrowshape: tuple[float, float, float] = ...,
        capstyle: Literal["round", "projecting", "butt"] = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        joinstyle: Literal["round", "bevel", "miter"] = ...,
        offset: _ScreenUnits = ...,
        smooth: bool = ...,
        splinesteps: float = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_line(
        self,
        coords: (
            tuple[float, float, float, float]
            | tuple[tuple[float, float], tuple[float, float]]
            | list[int]
            | list[float]
            | list[tuple[int, int]]
            | list[tuple[float, float]]
        ),
        /,
        *,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        arrow: Literal["first", "last", "both"] = ...,
        arrowshape: tuple[float, float, float] = ...,
        capstyle: Literal["round", "projecting", "butt"] = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        joinstyle: Literal["round", "bevel", "miter"] = ...,
        offset: _ScreenUnits = ...,
        smooth: bool = ...,
        splinesteps: float = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_oval(
        self,
        x0: float,
        y0: float,
        x1: float,
        y1: float,
        /,
        *,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activeoutline: str = ...,
        activeoutlinestipple: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledoutline: str = ...,
        disabledoutlinestipple: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        offset: _ScreenUnits = ...,
        outline: str = ...,
        outlineoffset: _ScreenUnits = ...,
        outlinestipple: str = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_oval(
        self,
        xy_pair_0: tuple[float, float],
        xy_pair_1: tuple[float, float],
        /,
        *,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activeoutline: str = ...,
        activeoutlinestipple: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledoutline: str = ...,
        disabledoutlinestipple: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        offset: _ScreenUnits = ...,
        outline: str = ...,
        outlineoffset: _ScreenUnits = ...,
        outlinestipple: str = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_oval(
        self,
        coords: (
            tuple[float, float, float, float]
            | tuple[tuple[float, float], tuple[float, float]]
            | list[int]
            | list[float]
            | list[tuple[int, int]]
            | list[tuple[float, float]]
        ),
        /,
        *,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activeoutline: str = ...,
        activeoutlinestipple: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledoutline: str = ...,
        disabledoutlinestipple: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        offset: _ScreenUnits = ...,
        outline: str = ...,
        outlineoffset: _ScreenUnits = ...,
        outlinestipple: str = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_polygon(
        self,
        x0: float,
        y0: float,
        x1: float,
        y1: float,
        /,
        *xy_pairs: float,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activeoutline: str = ...,
        activeoutlinestipple: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledoutline: str = ...,
        disabledoutlinestipple: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        joinstyle: Literal["round", "bevel", "miter"] = ...,
        offset: _ScreenUnits = ...,
        outline: str = ...,
        outlineoffset: _ScreenUnits = ...,
        outlinestipple: str = ...,
        smooth: bool = ...,
        splinesteps: float = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_polygon(
        self,
        xy_pair_0: tuple[float, float],
        xy_pair_1: tuple[float, float],
        /,
        *xy_pairs: tuple[float, float],
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activeoutline: str = ...,
        activeoutlinestipple: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledoutline: str = ...,
        disabledoutlinestipple: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        joinstyle: Literal["round", "bevel", "miter"] = ...,
        offset: _ScreenUnits = ...,
        outline: str = ...,
        outlineoffset: _ScreenUnits = ...,
        outlinestipple: str = ...,
        smooth: bool = ...,
        splinesteps: float = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_polygon(
        self,
        coords: (
            tuple[float, ...]
            | tuple[tuple[float, float], ...]
            | list[int]
            | list[float]
            | list[tuple[int, int]]
            | list[tuple[float, float]]
        ),
        /,
        *,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activeoutline: str = ...,
        activeoutlinestipple: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledoutline: str = ...,
        disabledoutlinestipple: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        joinstyle: Literal["round", "bevel", "miter"] = ...,
        offset: _ScreenUnits = ...,
        outline: str = ...,
        outlineoffset: _ScreenUnits = ...,
        outlinestipple: str = ...,
        smooth: bool = ...,
        splinesteps: float = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_rectangle(
        self,
        x0: float,
        y0: float,
        x1: float,
        y1: float,
        /,
        *,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activeoutline: str = ...,
        activeoutlinestipple: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledoutline: str = ...,
        disabledoutlinestipple: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        offset: _ScreenUnits = ...,
        outline: str = ...,
        outlineoffset: _ScreenUnits = ...,
        outlinestipple: str = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_rectangle(
        self,
        xy_pair_0: tuple[float, float],
        xy_pair_1: tuple[float, float],
        /,
        *,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activeoutline: str = ...,
        activeoutlinestipple: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledoutline: str = ...,
        disabledoutlinestipple: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        offset: _ScreenUnits = ...,
        outline: str = ...,
        outlineoffset: _ScreenUnits = ...,
        outlinestipple: str = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_rectangle(
        self,
        coords: (
            tuple[float, float, float, float]
            | tuple[tuple[float, float], tuple[float, float]]
            | list[int]
            | list[float]
            | list[tuple[int, int]]
            | list[tuple[float, float]]
        ),
        /,
        *,
        activedash: str | int | list[int] | tuple[int, ...] = ...,
        activefill: str = ...,
        activeoutline: str = ...,
        activeoutlinestipple: str = ...,
        activestipple: str = ...,
        activewidth: _ScreenUnits = ...,
        dash: str | int | list[int] | tuple[int, ...] = ...,
        dashoffset: _ScreenUnits = ...,
        disableddash: str | int | list[int] | tuple[int, ...] = ...,
        disabledfill: str = ...,
        disabledoutline: str = ...,
        disabledoutlinestipple: str = ...,
        disabledstipple: str = ...,
        disabledwidth: _ScreenUnits = ...,
        fill: str = ...,
        offset: _ScreenUnits = ...,
        outline: str = ...,
        outlineoffset: _ScreenUnits = ...,
        outlinestipple: str = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_text(
        self,
        x: float,
        y: float,
        /,
        *,
        activefill: str = ...,
        activestipple: str = ...,
        anchor: _Anchor = ...,
        angle: float | str = ...,
        disabledfill: str = ...,
        disabledstipple: str = ...,
        fill: str = ...,
        font: _FontDescription = ...,
        justify: Literal["left", "center", "right"] = ...,
        offset: _ScreenUnits = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        text: float | str = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_text(
        self,
        coords: tuple[float, float] | list[int] | list[float],
        /,
        *,
        activefill: str = ...,
        activestipple: str = ...,
        anchor: _Anchor = ...,
        angle: float | str = ...,
        disabledfill: str = ...,
        disabledstipple: str = ...,
        fill: str = ...,
        font: _FontDescription = ...,
        justify: Literal["left", "center", "right"] = ...,
        offset: _ScreenUnits = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        stipple: str = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        text: float | str = ...,
        width: _ScreenUnits = ...,
    ) -> int: ...
    @overload
    def create_window(
        self,
        x: float,
        y: float,
        /,
        *,
        anchor: _Anchor = ...,
        height: _ScreenUnits = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
        window: Widget = ...,
    ) -> int: ...
    @overload
    def create_window(
        self,
        coords: tuple[float, float] | list[int] | list[float],
        /,
        *,
        anchor: _Anchor = ...,
        height: _ScreenUnits = ...,
        state: Literal["normal", "hidden", "disabled"] = ...,
        tags: str | list[str] | tuple[str, ...] = ...,
        width: _ScreenUnits = ...,
        window: Widget = ...,
    ) -> int: ...
    def dchars(self, *args) -> None: ...
    def delete(self, *tagsOrCanvasIds: str | int) -> None: ...
    @overload
    def dtag(self, tag: str, tag_to_delete: str | None = ..., /) -> None: ...
    @overload
    def dtag(self, id: int, tag_to_delete: str, /) -> None: ...
    def focus(self, *args): ...
    def gettags(self, tagOrId: str | int, /) -> tuple[str, ...]: ...
    def icursor(self, *args) -> None: ...
    def index(self, *args): ...
    def insert(self, *args) -> None: ...
    def itemcget(self, tagOrId, option): ...
    # itemconfigure kwargs depend on item type, which is not known when type checking
    def itemconfigure(
        self, tagOrId: str | int, cnf: dict[str, Any] | None = None, **kw: Any
    ) -> dict[str, tuple[str, str, str, str, str]] | None: ...
    itemconfig = itemconfigure
    def move(self, *args) -> None: ...
    def moveto(self, tagOrId: str | int, x: Literal[""] | float = "", y: Literal[""] | float = "") -> None: ...
    def postscript(self, cnf={}, **kw): ...
    # tkinter does:
    #    lower = tag_lower
    #    lift = tkraise = tag_raise
    #
    # But mypy doesn't like aliasing here (maybe because Misc defines the same names)
    def tag_lower(self, first: str | int, second: str | int | None = ..., /) -> None: ...
    def lower(self, first: str | int, second: str | int | None = ..., /) -> None: ...  # type: ignore[override]
    def tag_raise(self, first: str | int, second: str | int | None = ..., /) -> None: ...
    def tkraise(self, first: str | int, second: str | int | None = ..., /) -> None: ...  # type: ignore[override]
    def lift(self, first: str | int, second: str | int | None = ..., /) -> None: ...  # type: ignore[override]
    def scale(
        self, tagOrId: str | int, xOrigin: _ScreenUnits, yOrigin: _ScreenUnits, xScale: float, yScale: float, /
    ) -> None: ...
    def scan_mark(self, x, y) -> None: ...
    def scan_dragto(self, x, y, gain: int = 10) -> None: ...
    def select_adjust(self, tagOrId, index) -> None: ...
    def select_clear(self) -> None: ...
    def select_from(self, tagOrId, index) -> None: ...
    def select_item(self): ...
    def select_to(self, tagOrId, index) -> None: ...
    def type(self, tagOrId: str | int) -> int | None: ...

class Checkbutton(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        activebackground: str = ...,
        activeforeground: str = ...,
        anchor: _Anchor = "center",
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        bitmap: str = "",
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        command: _ButtonCommand = "",
        compound: _Compound = "none",
        cursor: _Cursor = "",
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = "TkDefaultFont",
        foreground: str = ...,
        height: _ScreenUnits = 0,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = 1,
        image: _ImageSpec = "",
        indicatoron: bool = True,
        justify: Literal["left", "center", "right"] = "center",
        name: str = ...,
        offrelief: _Relief = ...,
        # The checkbutton puts a value to its variable when it's checked or
        # unchecked. We don't restrict the type of that value here, so
        # Any-typing is fine.
        #
        # I think Checkbutton shouldn't be generic, because then specifying
        # "any checkbutton regardless of what variable it uses" would be
        # difficult, and we might run into issues just like how list[float]
        # and list[int] are incompatible. Also, we would need a way to
        # specify "Checkbutton not associated with any variable", which is
        # done by setting variable to empty string (the default).
        offvalue: Any = 0,
        onvalue: Any = 1,
        overrelief: _Relief | Literal[""] = "",
        padx: _ScreenUnits = 1,
        pady: _ScreenUnits = 1,
        relief: _Relief = "flat",
        selectcolor: str = ...,
        selectimage: _ImageSpec = "",
        state: Literal["normal", "active", "disabled"] = "normal",
        takefocus: _TakeFocusValue = "",
        text: float | str = "",
        textvariable: Variable = ...,
        tristateimage: _ImageSpec = "",
        tristatevalue: Any = "",
        underline: int = -1,
        variable: Variable | Literal[""] = ...,
        width: _ScreenUnits = 0,
        wraplength: _ScreenUnits = 0,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        activebackground: str = ...,
        activeforeground: str = ...,
        anchor: _Anchor = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        bitmap: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        command: _ButtonCommand = ...,
        compound: _Compound = ...,
        cursor: _Cursor = ...,
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        image: _ImageSpec = ...,
        indicatoron: bool = ...,
        justify: Literal["left", "center", "right"] = ...,
        offrelief: _Relief = ...,
        offvalue: Any = ...,
        onvalue: Any = ...,
        overrelief: _Relief | Literal[""] = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        selectcolor: str = ...,
        selectimage: _ImageSpec = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        takefocus: _TakeFocusValue = ...,
        text: float | str = ...,
        textvariable: Variable = ...,
        tristateimage: _ImageSpec = ...,
        tristatevalue: Any = ...,
        underline: int = ...,
        variable: Variable | Literal[""] = ...,
        width: _ScreenUnits = ...,
        wraplength: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def deselect(self) -> None: ...
    def flash(self) -> None: ...
    def invoke(self) -> Any: ...
    def select(self) -> None: ...
    def toggle(self) -> None: ...

class Entry(Widget, XView):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = "xterm",
        disabledbackground: str = ...,
        disabledforeground: str = ...,
        exportselection: bool = True,
        fg: str = ...,
        font: _FontDescription = "TkTextFont",
        foreground: str = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        insertbackground: str = ...,
        insertborderwidth: _ScreenUnits = 0,
        insertofftime: int = 300,
        insertontime: int = 600,
        insertwidth: _ScreenUnits = ...,
        invalidcommand: _EntryValidateCommand = "",
        invcmd: _EntryValidateCommand = "",  # same as invalidcommand
        justify: Literal["left", "center", "right"] = "left",
        name: str = ...,
        readonlybackground: str = ...,
        relief: _Relief = "sunken",
        selectbackground: str = ...,
        selectborderwidth: _ScreenUnits = ...,
        selectforeground: str = ...,
        show: str = "",
        state: Literal["normal", "disabled", "readonly"] = "normal",
        takefocus: _TakeFocusValue = "",
        textvariable: Variable = ...,
        validate: Literal["none", "focus", "focusin", "focusout", "key", "all"] = "none",
        validatecommand: _EntryValidateCommand = "",
        vcmd: _EntryValidateCommand = "",  # same as validatecommand
        width: int = 20,
        xscrollcommand: _XYScrollCommand = "",
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = ...,
        disabledbackground: str = ...,
        disabledforeground: str = ...,
        exportselection: bool = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        insertbackground: str = ...,
        insertborderwidth: _ScreenUnits = ...,
        insertofftime: int = ...,
        insertontime: int = ...,
        insertwidth: _ScreenUnits = ...,
        invalidcommand: _EntryValidateCommand = ...,
        invcmd: _EntryValidateCommand = ...,
        justify: Literal["left", "center", "right"] = ...,
        readonlybackground: str = ...,
        relief: _Relief = ...,
        selectbackground: str = ...,
        selectborderwidth: _ScreenUnits = ...,
        selectforeground: str = ...,
        show: str = ...,
        state: Literal["normal", "disabled", "readonly"] = ...,
        takefocus: _TakeFocusValue = ...,
        textvariable: Variable = ...,
        validate: Literal["none", "focus", "focusin", "focusout", "key", "all"] = ...,
        validatecommand: _EntryValidateCommand = ...,
        vcmd: _EntryValidateCommand = ...,
        width: int = ...,
        xscrollcommand: _XYScrollCommand = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def delete(self, first: str | int, last: str | int | None = None) -> None: ...
    def get(self) -> str: ...
    def icursor(self, index: str | int) -> None: ...
    def index(self, index: str | int) -> int: ...
    def insert(self, index: str | int, string: str) -> None: ...
    def scan_mark(self, x) -> None: ...
    def scan_dragto(self, x) -> None: ...
    def selection_adjust(self, index: str | int) -> None: ...
    def selection_clear(self) -> None: ...  # type: ignore[override]
    def selection_from(self, index: str | int) -> None: ...
    def selection_present(self) -> bool: ...
    def selection_range(self, start: str | int, end: str | int) -> None: ...
    def selection_to(self, index: str | int) -> None: ...
    select_adjust = selection_adjust
    select_clear = selection_clear
    select_from = selection_from
    select_present = selection_present
    select_range = selection_range
    select_to = selection_to

class Frame(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        background: str = ...,
        bd: _ScreenUnits = 0,
        bg: str = ...,
        border: _ScreenUnits = 0,
        borderwidth: _ScreenUnits = 0,
        class_: str = "Frame",  # can't be changed with configure()
        colormap: Literal["new", ""] | Misc = "",  # can't be changed with configure()
        container: bool = False,  # can't be changed with configure()
        cursor: _Cursor = "",
        height: _ScreenUnits = 0,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = 0,
        name: str = ...,
        padx: _ScreenUnits = 0,
        pady: _ScreenUnits = 0,
        relief: _Relief = "flat",
        takefocus: _TakeFocusValue = 0,
        visual: str | tuple[str, int] = "",  # can't be changed with configure()
        width: _ScreenUnits = 0,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        takefocus: _TakeFocusValue = ...,
        width: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure

class Label(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        activebackground: str = ...,
        activeforeground: str = ...,
        anchor: _Anchor = "center",
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        bitmap: str = "",
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        compound: _Compound = "none",
        cursor: _Cursor = "",
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = "TkDefaultFont",
        foreground: str = ...,
        height: _ScreenUnits = 0,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = 0,
        image: _ImageSpec = "",
        justify: Literal["left", "center", "right"] = "center",
        name: str = ...,
        padx: _ScreenUnits = 1,
        pady: _ScreenUnits = 1,
        relief: _Relief = "flat",
        state: Literal["normal", "active", "disabled"] = "normal",
        takefocus: _TakeFocusValue = 0,
        text: float | str = "",
        textvariable: Variable = ...,
        underline: int = -1,
        width: _ScreenUnits = 0,
        wraplength: _ScreenUnits = 0,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        activebackground: str = ...,
        activeforeground: str = ...,
        anchor: _Anchor = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        bitmap: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        compound: _Compound = ...,
        cursor: _Cursor = ...,
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        image: _ImageSpec = ...,
        justify: Literal["left", "center", "right"] = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        takefocus: _TakeFocusValue = ...,
        text: float | str = ...,
        textvariable: Variable = ...,
        underline: int = ...,
        width: _ScreenUnits = ...,
        wraplength: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure

class Listbox(Widget, XView, YView):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        activestyle: Literal["dotbox", "none", "underline"] = ...,
        background: str = ...,
        bd: _ScreenUnits = 1,
        bg: str = ...,
        border: _ScreenUnits = 1,
        borderwidth: _ScreenUnits = 1,
        cursor: _Cursor = "",
        disabledforeground: str = ...,
        exportselection: bool | Literal[0, 1] = 1,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        height: int = 10,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        justify: Literal["left", "center", "right"] = "left",
        # There's no tkinter.ListVar, but seems like bare tkinter.Variable
        # actually works for this:
        #
        #    >>> import tkinter
        #    >>> lb = tkinter.Listbox()
        #    >>> var = lb['listvariable'] = tkinter.Variable()
        #    >>> var.set(['foo', 'bar', 'baz'])
        #    >>> lb.get(0, 'end')
        #    ('foo', 'bar', 'baz')
        listvariable: Variable = ...,
        name: str = ...,
        relief: _Relief = ...,
        selectbackground: str = ...,
        selectborderwidth: _ScreenUnits = 0,
        selectforeground: str = ...,
        # from listbox man page: "The value of the [selectmode] option may be
        # arbitrary, but the default bindings expect it to be either single,
        # browse, multiple, or extended"
        #
        # I have never seen anyone setting this to something else than what
        # "the default bindings expect", but let's support it anyway.
        selectmode: str | Literal["single", "browse", "multiple", "extended"] = "browse",  # noqa: Y051
        setgrid: bool = False,
        state: Literal["normal", "disabled"] = "normal",
        takefocus: _TakeFocusValue = "",
        width: int = 20,
        xscrollcommand: _XYScrollCommand = "",
        yscrollcommand: _XYScrollCommand = "",
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        activestyle: Literal["dotbox", "none", "underline"] = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = ...,
        disabledforeground: str = ...,
        exportselection: bool = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        height: int = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        justify: Literal["left", "center", "right"] = ...,
        listvariable: Variable = ...,
        relief: _Relief = ...,
        selectbackground: str = ...,
        selectborderwidth: _ScreenUnits = ...,
        selectforeground: str = ...,
        selectmode: str | Literal["single", "browse", "multiple", "extended"] = ...,  # noqa: Y051
        setgrid: bool = ...,
        state: Literal["normal", "disabled"] = ...,
        takefocus: _TakeFocusValue = ...,
        width: int = ...,
        xscrollcommand: _XYScrollCommand = ...,
        yscrollcommand: _XYScrollCommand = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def activate(self, index: str | int) -> None: ...
    def bbox(self, index: str | int) -> tuple[int, int, int, int] | None: ...  # type: ignore[override]
    def curselection(self): ...
    def delete(self, first: str | int, last: str | int | None = None) -> None: ...
    def get(self, first: str | int, last: str | int | None = None): ...
    def index(self, index: str | int) -> int: ...
    def insert(self, index: str | int, *elements: str | float) -> None: ...
    def nearest(self, y): ...
    def scan_mark(self, x, y) -> None: ...
    def scan_dragto(self, x, y) -> None: ...
    def see(self, index: str | int) -> None: ...
    def selection_anchor(self, index: str | int) -> None: ...
    select_anchor = selection_anchor
    def selection_clear(self, first: str | int, last: str | int | None = None) -> None: ...  # type: ignore[override]
    select_clear = selection_clear
    def selection_includes(self, index: str | int): ...
    select_includes = selection_includes
    def selection_set(self, first: str | int, last: str | int | None = None) -> None: ...
    select_set = selection_set
    def size(self) -> int: ...  # type: ignore[override]
    def itemcget(self, index: str | int, option): ...
    def itemconfigure(self, index: str | int, cnf=None, **kw): ...
    itemconfig = itemconfigure

class Menu(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        activebackground: str = ...,
        activeborderwidth: _ScreenUnits = ...,
        activeforeground: str = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = "arrow",
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        name: str = ...,
        postcommand: Callable[[], object] | str = "",
        relief: _Relief = ...,
        selectcolor: str = ...,
        takefocus: _TakeFocusValue = 0,
        tearoff: bool | Literal[0, 1] = 1,
        # I guess tearoffcommand arguments are supposed to be widget objects,
        # but they are widget name strings. Use nametowidget() to handle the
        # arguments of tearoffcommand.
        tearoffcommand: Callable[[str, str], object] | str = "",
        title: str = "",
        type: Literal["menubar", "tearoff", "normal"] = "normal",
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        activebackground: str = ...,
        activeborderwidth: _ScreenUnits = ...,
        activeforeground: str = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = ...,
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        postcommand: Callable[[], object] | str = ...,
        relief: _Relief = ...,
        selectcolor: str = ...,
        takefocus: _TakeFocusValue = ...,
        tearoff: bool = ...,
        tearoffcommand: Callable[[str, str], object] | str = ...,
        title: str = ...,
        type: Literal["menubar", "tearoff", "normal"] = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def tk_popup(self, x: int, y: int, entry: str | int = "") -> None: ...
    def activate(self, index: str | int) -> None: ...
    def add(self, itemType, cnf={}, **kw): ...  # docstring says "Internal function."
    def insert(self, index, itemType, cnf={}, **kw): ...  # docstring says "Internal function."
    def add_cascade(
        self,
        cnf: dict[str, Any] | None = {},
        *,
        accelerator: str = ...,
        activebackground: str = ...,
        activeforeground: str = ...,
        background: str = ...,
        bitmap: str = ...,
        columnbreak: int = ...,
        command: Callable[[], object] | str = ...,
        compound: _Compound = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        hidemargin: bool = ...,
        image: _ImageSpec = ...,
        label: str = ...,
        menu: Menu = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        underline: int = ...,
    ) -> None: ...
    def add_checkbutton(
        self,
        cnf: dict[str, Any] | None = {},
        *,
        accelerator: str = ...,
        activebackground: str = ...,
        activeforeground: str = ...,
        background: str = ...,
        bitmap: str = ...,
        columnbreak: int = ...,
        command: Callable[[], object] | str = ...,
        compound: _Compound = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        hidemargin: bool = ...,
        image: _ImageSpec = ...,
        indicatoron: bool = ...,
        label: str = ...,
        offvalue: Any = ...,
        onvalue: Any = ...,
        selectcolor: str = ...,
        selectimage: _ImageSpec = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        underline: int = ...,
        variable: Variable = ...,
    ) -> None: ...
    def add_command(
        self,
        cnf: dict[str, Any] | None = {},
        *,
        accelerator: str = ...,
        activebackground: str = ...,
        activeforeground: str = ...,
        background: str = ...,
        bitmap: str = ...,
        columnbreak: int = ...,
        command: Callable[[], object] | str = ...,
        compound: _Compound = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        hidemargin: bool = ...,
        image: _ImageSpec = ...,
        label: str = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        underline: int = ...,
    ) -> None: ...
    def add_radiobutton(
        self,
        cnf: dict[str, Any] | None = {},
        *,
        accelerator: str = ...,
        activebackground: str = ...,
        activeforeground: str = ...,
        background: str = ...,
        bitmap: str = ...,
        columnbreak: int = ...,
        command: Callable[[], object] | str = ...,
        compound: _Compound = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        hidemargin: bool = ...,
        image: _ImageSpec = ...,
        indicatoron: bool = ...,
        label: str = ...,
        selectcolor: str = ...,
        selectimage: _ImageSpec = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        underline: int = ...,
        value: Any = ...,
        variable: Variable = ...,
    ) -> None: ...
    def add_separator(self, cnf: dict[str, Any] | None = {}, *, background: str = ...) -> None: ...
    def insert_cascade(
        self,
        index: str | int,
        cnf: dict[str, Any] | None = {},
        *,
        accelerator: str = ...,
        activebackground: str = ...,
        activeforeground: str = ...,
        background: str = ...,
        bitmap: str = ...,
        columnbreak: int = ...,
        command: Callable[[], object] | str = ...,
        compound: _Compound = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        hidemargin: bool = ...,
        image: _ImageSpec = ...,
        label: str = ...,
        menu: Menu = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        underline: int = ...,
    ) -> None: ...
    def insert_checkbutton(
        self,
        index: str | int,
        cnf: dict[str, Any] | None = {},
        *,
        accelerator: str = ...,
        activebackground: str = ...,
        activeforeground: str = ...,
        background: str = ...,
        bitmap: str = ...,
        columnbreak: int = ...,
        command: Callable[[], object] | str = ...,
        compound: _Compound = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        hidemargin: bool = ...,
        image: _ImageSpec = ...,
        indicatoron: bool = ...,
        label: str = ...,
        offvalue: Any = ...,
        onvalue: Any = ...,
        selectcolor: str = ...,
        selectimage: _ImageSpec = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        underline: int = ...,
        variable: Variable = ...,
    ) -> None: ...
    def insert_command(
        self,
        index: str | int,
        cnf: dict[str, Any] | None = {},
        *,
        accelerator: str = ...,
        activebackground: str = ...,
        activeforeground: str = ...,
        background: str = ...,
        bitmap: str = ...,
        columnbreak: int = ...,
        command: Callable[[], object] | str = ...,
        compound: _Compound = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        hidemargin: bool = ...,
        image: _ImageSpec = ...,
        label: str = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        underline: int = ...,
    ) -> None: ...
    def insert_radiobutton(
        self,
        index: str | int,
        cnf: dict[str, Any] | None = {},
        *,
        accelerator: str = ...,
        activebackground: str = ...,
        activeforeground: str = ...,
        background: str = ...,
        bitmap: str = ...,
        columnbreak: int = ...,
        command: Callable[[], object] | str = ...,
        compound: _Compound = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        hidemargin: bool = ...,
        image: _ImageSpec = ...,
        indicatoron: bool = ...,
        label: str = ...,
        selectcolor: str = ...,
        selectimage: _ImageSpec = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        underline: int = ...,
        value: Any = ...,
        variable: Variable = ...,
    ) -> None: ...
    def insert_separator(self, index: str | int, cnf: dict[str, Any] | None = {}, *, background: str = ...) -> None: ...
    def delete(self, index1: str | int, index2: str | int | None = None) -> None: ...
    def entrycget(self, index: str | int, option: str) -> Any: ...
    def entryconfigure(
        self, index: str | int, cnf: dict[str, Any] | None = None, **kw: Any
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    entryconfig = entryconfigure
    def index(self, index: str | int) -> int | None: ...
    def invoke(self, index: str | int) -> Any: ...
    def post(self, x: int, y: int) -> None: ...
    def type(self, index: str | int) -> Literal["cascade", "checkbutton", "command", "radiobutton", "separator"]: ...
    def unpost(self) -> None: ...
    def xposition(self, index: str | int) -> int: ...
    def yposition(self, index: str | int) -> int: ...

class Menubutton(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        activebackground: str = ...,
        activeforeground: str = ...,
        anchor: _Anchor = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        bitmap: str = "",
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        compound: _Compound = "none",
        cursor: _Cursor = "",
        direction: Literal["above", "below", "left", "right", "flush"] = "below",
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = "TkDefaultFont",
        foreground: str = ...,
        height: _ScreenUnits = 0,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = 0,
        image: _ImageSpec = "",
        indicatoron: bool = ...,
        justify: Literal["left", "center", "right"] = ...,
        menu: Menu = ...,
        name: str = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = "flat",
        state: Literal["normal", "active", "disabled"] = "normal",
        takefocus: _TakeFocusValue = 0,
        text: float | str = "",
        textvariable: Variable = ...,
        underline: int = -1,
        width: _ScreenUnits = 0,
        wraplength: _ScreenUnits = 0,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        activebackground: str = ...,
        activeforeground: str = ...,
        anchor: _Anchor = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        bitmap: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        compound: _Compound = ...,
        cursor: _Cursor = ...,
        direction: Literal["above", "below", "left", "right", "flush"] = ...,
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        image: _ImageSpec = ...,
        indicatoron: bool = ...,
        justify: Literal["left", "center", "right"] = ...,
        menu: Menu = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        takefocus: _TakeFocusValue = ...,
        text: float | str = ...,
        textvariable: Variable = ...,
        underline: int = ...,
        width: _ScreenUnits = ...,
        wraplength: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure

class Message(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        anchor: _Anchor = "center",
        aspect: int = 150,
        background: str = ...,
        bd: _ScreenUnits = 1,
        bg: str = ...,
        border: _ScreenUnits = 1,
        borderwidth: _ScreenUnits = 1,
        cursor: _Cursor = "",
        fg: str = ...,
        font: _FontDescription = "TkDefaultFont",
        foreground: str = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = 0,
        justify: Literal["left", "center", "right"] = "left",
        name: str = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = "flat",
        takefocus: _TakeFocusValue = 0,
        text: float | str = "",
        textvariable: Variable = ...,
        # there's width but no height
        width: _ScreenUnits = 0,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        anchor: _Anchor = ...,
        aspect: int = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        justify: Literal["left", "center", "right"] = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        takefocus: _TakeFocusValue = ...,
        text: float | str = ...,
        textvariable: Variable = ...,
        width: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure

class Radiobutton(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        activebackground: str = ...,
        activeforeground: str = ...,
        anchor: _Anchor = "center",
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        bitmap: str = "",
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        command: _ButtonCommand = "",
        compound: _Compound = "none",
        cursor: _Cursor = "",
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = "TkDefaultFont",
        foreground: str = ...,
        height: _ScreenUnits = 0,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = 1,
        image: _ImageSpec = "",
        indicatoron: bool = True,
        justify: Literal["left", "center", "right"] = "center",
        name: str = ...,
        offrelief: _Relief = ...,
        overrelief: _Relief | Literal[""] = "",
        padx: _ScreenUnits = 1,
        pady: _ScreenUnits = 1,
        relief: _Relief = "flat",
        selectcolor: str = ...,
        selectimage: _ImageSpec = "",
        state: Literal["normal", "active", "disabled"] = "normal",
        takefocus: _TakeFocusValue = "",
        text: float | str = "",
        textvariable: Variable = ...,
        tristateimage: _ImageSpec = "",
        tristatevalue: Any = "",
        underline: int = -1,
        value: Any = "",
        variable: Variable | Literal[""] = ...,
        width: _ScreenUnits = 0,
        wraplength: _ScreenUnits = 0,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        activebackground: str = ...,
        activeforeground: str = ...,
        anchor: _Anchor = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        bitmap: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        command: _ButtonCommand = ...,
        compound: _Compound = ...,
        cursor: _Cursor = ...,
        disabledforeground: str = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        image: _ImageSpec = ...,
        indicatoron: bool = ...,
        justify: Literal["left", "center", "right"] = ...,
        offrelief: _Relief = ...,
        overrelief: _Relief | Literal[""] = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        selectcolor: str = ...,
        selectimage: _ImageSpec = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        takefocus: _TakeFocusValue = ...,
        text: float | str = ...,
        textvariable: Variable = ...,
        tristateimage: _ImageSpec = ...,
        tristatevalue: Any = ...,
        underline: int = ...,
        value: Any = ...,
        variable: Variable | Literal[""] = ...,
        width: _ScreenUnits = ...,
        wraplength: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def deselect(self) -> None: ...
    def flash(self) -> None: ...
    def invoke(self) -> Any: ...
    def select(self) -> None: ...

class Scale(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        activebackground: str = ...,
        background: str = ...,
        bd: _ScreenUnits = 1,
        bg: str = ...,
        bigincrement: float = 0.0,
        border: _ScreenUnits = 1,
        borderwidth: _ScreenUnits = 1,
        # don't know why the callback gets string instead of float
        command: str | Callable[[str], object] = "",
        cursor: _Cursor = "",
        digits: int = 0,
        fg: str = ...,
        font: _FontDescription = "TkDefaultFont",
        foreground: str = ...,
        from_: float = 0.0,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        label: str = "",
        length: _ScreenUnits = 100,
        name: str = ...,
        orient: Literal["horizontal", "vertical"] = "vertical",
        relief: _Relief = "flat",
        repeatdelay: int = 300,
        repeatinterval: int = 100,
        resolution: float = 1.0,
        showvalue: bool = True,
        sliderlength: _ScreenUnits = 30,
        sliderrelief: _Relief = "raised",
        state: Literal["normal", "active", "disabled"] = "normal",
        takefocus: _TakeFocusValue = "",
        tickinterval: float = 0.0,
        to: float = 100.0,
        troughcolor: str = ...,
        variable: IntVar | DoubleVar = ...,
        width: _ScreenUnits = 15,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        activebackground: str = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        bigincrement: float = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        command: str | Callable[[str], object] = ...,
        cursor: _Cursor = ...,
        digits: int = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        from_: float = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        label: str = ...,
        length: _ScreenUnits = ...,
        orient: Literal["horizontal", "vertical"] = ...,
        relief: _Relief = ...,
        repeatdelay: int = ...,
        repeatinterval: int = ...,
        resolution: float = ...,
        showvalue: bool = ...,
        sliderlength: _ScreenUnits = ...,
        sliderrelief: _Relief = ...,
        state: Literal["normal", "active", "disabled"] = ...,
        takefocus: _TakeFocusValue = ...,
        tickinterval: float = ...,
        to: float = ...,
        troughcolor: str = ...,
        variable: IntVar | DoubleVar = ...,
        width: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def get(self) -> float: ...
    def set(self, value) -> None: ...
    def coords(self, value: float | None = None) -> tuple[int, int]: ...
    def identify(self, x, y) -> Literal["", "slider", "trough1", "trough2"]: ...

class Scrollbar(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        activebackground: str = ...,
        activerelief: _Relief = "raised",
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        # There are many ways how the command may get called. Search for
        # 'SCROLLING COMMANDS' in scrollbar man page. There doesn't seem to
        # be any way to specify an overloaded callback function, so we say
        # that it can take any args while it can't in reality.
        command: Callable[..., tuple[float, float] | None] | str = "",
        cursor: _Cursor = "",
        elementborderwidth: _ScreenUnits = -1,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = 0,
        jump: bool = False,
        name: str = ...,
        orient: Literal["horizontal", "vertical"] = "vertical",
        relief: _Relief = ...,
        repeatdelay: int = 300,
        repeatinterval: int = 100,
        takefocus: _TakeFocusValue = "",
        troughcolor: str = ...,
        width: _ScreenUnits = ...,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        activebackground: str = ...,
        activerelief: _Relief = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        command: Callable[..., tuple[float, float] | None] | str = ...,
        cursor: _Cursor = ...,
        elementborderwidth: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        jump: bool = ...,
        orient: Literal["horizontal", "vertical"] = ...,
        relief: _Relief = ...,
        repeatdelay: int = ...,
        repeatinterval: int = ...,
        takefocus: _TakeFocusValue = ...,
        troughcolor: str = ...,
        width: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def activate(self, index=None): ...
    def delta(self, deltax: int, deltay: int) -> float: ...
    def fraction(self, x: int, y: int) -> float: ...
    def identify(self, x: int, y: int) -> Literal["arrow1", "arrow2", "slider", "trough1", "trough2", ""]: ...
    def get(self) -> tuple[float, float, float, float] | tuple[float, float]: ...
    def set(self, first: float | str, last: float | str) -> None: ...

_TextIndex: TypeAlias = _tkinter.Tcl_Obj | str | float | Misc
_WhatToCount: TypeAlias = Literal[
    "chars", "displaychars", "displayindices", "displaylines", "indices", "lines", "xpixels", "ypixels"
]

class Text(Widget, XView, YView):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        autoseparators: bool = True,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        blockcursor: bool = False,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = "xterm",
        endline: int | Literal[""] = "",
        exportselection: bool = True,
        fg: str = ...,
        font: _FontDescription = "TkFixedFont",
        foreground: str = ...,
        # width is always int, but height is allowed to be ScreenUnits.
        # This doesn't make any sense to me, and this isn't documented.
        # The docs seem to say that both should be integers.
        height: _ScreenUnits = 24,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        inactiveselectbackground: str = ...,
        insertbackground: str = ...,
        insertborderwidth: _ScreenUnits = 0,
        insertofftime: int = 300,
        insertontime: int = 600,
        insertunfocussed: Literal["none", "hollow", "solid"] = "none",
        insertwidth: _ScreenUnits = ...,
        maxundo: int = 0,
        name: str = ...,
        padx: _ScreenUnits = 1,
        pady: _ScreenUnits = 1,
        relief: _Relief = ...,
        selectbackground: str = ...,
        selectborderwidth: _ScreenUnits = ...,
        selectforeground: str = ...,
        setgrid: bool = False,
        spacing1: _ScreenUnits = 0,
        spacing2: _ScreenUnits = 0,
        spacing3: _ScreenUnits = 0,
        startline: int | Literal[""] = "",
        state: Literal["normal", "disabled"] = "normal",
        # Literal inside Tuple doesn't actually work
        tabs: _ScreenUnits | str | tuple[_ScreenUnits | str, ...] = "",
        tabstyle: Literal["tabular", "wordprocessor"] = "tabular",
        takefocus: _TakeFocusValue = "",
        undo: bool = False,
        width: int = 80,
        wrap: Literal["none", "char", "word"] = "char",
        xscrollcommand: _XYScrollCommand = "",
        yscrollcommand: _XYScrollCommand = "",
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        autoseparators: bool = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        blockcursor: bool = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = ...,
        endline: int | Literal[""] = ...,
        exportselection: bool = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        inactiveselectbackground: str = ...,
        insertbackground: str = ...,
        insertborderwidth: _ScreenUnits = ...,
        insertofftime: int = ...,
        insertontime: int = ...,
        insertunfocussed: Literal["none", "hollow", "solid"] = ...,
        insertwidth: _ScreenUnits = ...,
        maxundo: int = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        selectbackground: str = ...,
        selectborderwidth: _ScreenUnits = ...,
        selectforeground: str = ...,
        setgrid: bool = ...,
        spacing1: _ScreenUnits = ...,
        spacing2: _ScreenUnits = ...,
        spacing3: _ScreenUnits = ...,
        startline: int | Literal[""] = ...,
        state: Literal["normal", "disabled"] = ...,
        tabs: _ScreenUnits | str | tuple[_ScreenUnits | str, ...] = ...,
        tabstyle: Literal["tabular", "wordprocessor"] = ...,
        takefocus: _TakeFocusValue = ...,
        undo: bool = ...,
        width: int = ...,
        wrap: Literal["none", "char", "word"] = ...,
        xscrollcommand: _XYScrollCommand = ...,
        yscrollcommand: _XYScrollCommand = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def bbox(self, index: _TextIndex) -> tuple[int, int, int, int] | None: ...  # type: ignore[override]
    def compare(self, index1: _TextIndex, op: Literal["<", "<=", "==", ">=", ">", "!="], index2: _TextIndex) -> bool: ...
    if sys.version_info >= (3, 13):
        @overload
        def count(self, index1: _TextIndex, index2: _TextIndex, *, return_ints: Literal[True]) -> int: ...
        @overload
        def count(
            self, index1: _TextIndex, index2: _TextIndex, arg: _WhatToCount | Literal["update"], /, *, return_ints: Literal[True]
        ) -> int: ...
        @overload
        def count(
            self,
            index1: _TextIndex,
            index2: _TextIndex,
            arg1: Literal["update"],
            arg2: _WhatToCount,
            /,
            *,
            return_ints: Literal[True],
        ) -> int: ...
        @overload
        def count(
            self,
            index1: _TextIndex,
            index2: _TextIndex,
            arg1: _WhatToCount,
            arg2: Literal["update"],
            /,
            *,
            return_ints: Literal[True],
        ) -> int: ...
        @overload
        def count(
            self, index1: _TextIndex, index2: _TextIndex, arg1: _WhatToCount, arg2: _WhatToCount, /, *, return_ints: Literal[True]
        ) -> tuple[int, int]: ...
        @overload
        def count(
            self,
            index1: _TextIndex,
            index2: _TextIndex,
            arg1: _WhatToCount | Literal["update"],
            arg2: _WhatToCount | Literal["update"],
            arg3: _WhatToCount | Literal["update"],
            /,
            *args: _WhatToCount | Literal["update"],
            return_ints: Literal[True],
        ) -> tuple[int, ...]: ...
        @overload
        def count(self, index1: _TextIndex, index2: _TextIndex, *, return_ints: Literal[False] = False) -> tuple[int] | None: ...
        @overload
        def count(
            self,
            index1: _TextIndex,
            index2: _TextIndex,
            arg: _WhatToCount | Literal["update"],
            /,
            *,
            return_ints: Literal[False] = False,
        ) -> tuple[int] | None: ...
        @overload
        def count(
            self,
            index1: _TextIndex,
            index2: _TextIndex,
            arg1: Literal["update"],
            arg2: _WhatToCount,
            /,
            *,
            return_ints: Literal[False] = False,
        ) -> int | None: ...
        @overload
        def count(
            self,
            index1: _TextIndex,
            index2: _TextIndex,
            arg1: _WhatToCount,
            arg2: Literal["update"],
            /,
            *,
            return_ints: Literal[False] = False,
        ) -> int | None: ...
        @overload
        def count(
            self,
            index1: _TextIndex,
            index2: _TextIndex,
            arg1: _WhatToCount,
            arg2: _WhatToCount,
            /,
            *,
            return_ints: Literal[False] = False,
        ) -> tuple[int, int]: ...
        @overload
        def count(
            self,
            index1: _TextIndex,
            index2: _TextIndex,
            arg1: _WhatToCount | Literal["update"],
            arg2: _WhatToCount | Literal["update"],
            arg3: _WhatToCount | Literal["update"],
            /,
            *args: _WhatToCount | Literal["update"],
            return_ints: Literal[False] = False,
        ) -> tuple[int, ...]: ...
    else:
        @overload
        def count(self, index1: _TextIndex, index2: _TextIndex) -> tuple[int] | None: ...
        @overload
        def count(
            self, index1: _TextIndex, index2: _TextIndex, arg: _WhatToCount | Literal["update"], /
        ) -> tuple[int] | None: ...
        @overload
        def count(self, index1: _TextIndex, index2: _TextIndex, arg1: Literal["update"], arg2: _WhatToCount, /) -> int | None: ...
        @overload
        def count(self, index1: _TextIndex, index2: _TextIndex, arg1: _WhatToCount, arg2: Literal["update"], /) -> int | None: ...
        @overload
        def count(self, index1: _TextIndex, index2: _TextIndex, arg1: _WhatToCount, arg2: _WhatToCount, /) -> tuple[int, int]: ...
        @overload
        def count(
            self,
            index1: _TextIndex,
            index2: _TextIndex,
            arg1: _WhatToCount | Literal["update"],
            arg2: _WhatToCount | Literal["update"],
            arg3: _WhatToCount | Literal["update"],
            /,
            *args: _WhatToCount | Literal["update"],
        ) -> tuple[int, ...]: ...

    @overload
    def debug(self, boolean: None = None) -> bool: ...
    @overload
    def debug(self, boolean: bool) -> None: ...
    def delete(self, index1: _TextIndex, index2: _TextIndex | None = None) -> None: ...
    def dlineinfo(self, index: _TextIndex) -> tuple[int, int, int, int, int] | None: ...
    @overload
    def dump(
        self,
        index1: _TextIndex,
        index2: _TextIndex | None = None,
        command: None = None,
        *,
        all: bool = ...,
        image: bool = ...,
        mark: bool = ...,
        tag: bool = ...,
        text: bool = ...,
        window: bool = ...,
    ) -> list[tuple[str, str, str]]: ...
    @overload
    def dump(
        self,
        index1: _TextIndex,
        index2: _TextIndex | None,
        command: Callable[[str, str, str], object] | str,
        *,
        all: bool = ...,
        image: bool = ...,
        mark: bool = ...,
        tag: bool = ...,
        text: bool = ...,
        window: bool = ...,
    ) -> None: ...
    @overload
    def dump(
        self,
        index1: _TextIndex,
        index2: _TextIndex | None = None,
        *,
        command: Callable[[str, str, str], object] | str,
        all: bool = ...,
        image: bool = ...,
        mark: bool = ...,
        tag: bool = ...,
        text: bool = ...,
        window: bool = ...,
    ) -> None: ...
    def edit(self, *args): ...  # docstring says "Internal method"
    @overload
    def edit_modified(self, arg: None = None) -> bool: ...  # actually returns Literal[0, 1]
    @overload
    def edit_modified(self, arg: bool) -> None: ...  # actually returns empty string
    def edit_redo(self) -> None: ...  # actually returns empty string
    def edit_reset(self) -> None: ...  # actually returns empty string
    def edit_separator(self) -> None: ...  # actually returns empty string
    def edit_undo(self) -> None: ...  # actually returns empty string
    def get(self, index1: _TextIndex, index2: _TextIndex | None = None) -> str: ...
    @overload
    def image_cget(self, index: _TextIndex, option: Literal["image", "name"]) -> str: ...
    @overload
    def image_cget(self, index: _TextIndex, option: Literal["padx", "pady"]) -> int: ...
    @overload
    def image_cget(self, index: _TextIndex, option: Literal["align"]) -> Literal["baseline", "bottom", "center", "top"]: ...
    @overload
    def image_cget(self, index: _TextIndex, option: str) -> Any: ...
    @overload
    def image_configure(self, index: _TextIndex, cnf: str) -> tuple[str, str, str, str, str | int]: ...
    @overload
    def image_configure(
        self,
        index: _TextIndex,
        cnf: dict[str, Any] | None = {},
        *,
        align: Literal["baseline", "bottom", "center", "top"] = ...,
        image: _ImageSpec = ...,
        name: str = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, str, str | int]] | None: ...
    def image_create(
        self,
        index: _TextIndex,
        cnf: dict[str, Any] | None = {},
        *,
        align: Literal["baseline", "bottom", "center", "top"] = ...,
        image: _ImageSpec = ...,
        name: str = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
    ) -> str: ...
    def image_names(self) -> tuple[str, ...]: ...
    def index(self, index: _TextIndex) -> str: ...
    def insert(self, index: _TextIndex, chars: str, *args: str | list[str] | tuple[str, ...]) -> None: ...
    @overload
    def mark_gravity(self, markName: str, direction: None = None) -> Literal["left", "right"]: ...
    @overload
    def mark_gravity(self, markName: str, direction: Literal["left", "right"]) -> None: ...  # actually returns empty string
    def mark_names(self) -> tuple[str, ...]: ...
    def mark_set(self, markName: str, index: _TextIndex) -> None: ...
    def mark_unset(self, *markNames: str) -> None: ...
    def mark_next(self, index: _TextIndex) -> str | None: ...
    def mark_previous(self, index: _TextIndex) -> str | None: ...
    # **kw of peer_create is same as the kwargs of Text.__init__
    def peer_create(self, newPathName: str | Text, cnf: dict[str, Any] = {}, **kw) -> None: ...
    def peer_names(self) -> tuple[_tkinter.Tcl_Obj, ...]: ...
    def replace(self, index1: _TextIndex, index2: _TextIndex, chars: str, *args: str | list[str] | tuple[str, ...]) -> None: ...
    def scan_mark(self, x: int, y: int) -> None: ...
    def scan_dragto(self, x: int, y: int) -> None: ...
    def search(
        self,
        pattern: str,
        index: _TextIndex,
        stopindex: _TextIndex | None = None,
        forwards: bool | None = None,
        backwards: bool | None = None,
        exact: bool | None = None,
        regexp: bool | None = None,
        nocase: bool | None = None,
        count: Variable | None = None,
        elide: bool | None = None,
    ) -> str: ...  # returns empty string for not found
    def see(self, index: _TextIndex) -> None: ...
    def tag_add(self, tagName: str, index1: _TextIndex, *args: _TextIndex) -> None: ...
    # tag_bind stuff is very similar to Canvas
    @overload
    def tag_bind(
        self,
        tagName: str,
        sequence: str | None,
        func: Callable[[Event[Text]], object] | None,
        add: Literal["", "+"] | bool | None = None,
    ) -> str: ...
    @overload
    def tag_bind(self, tagName: str, sequence: str | None, func: str, add: Literal["", "+"] | bool | None = None) -> None: ...
    def tag_unbind(self, tagName: str, sequence: str, funcid: str | None = None) -> None: ...
    # allowing any string for cget instead of just Literals because there's no other way to look up tag options
    def tag_cget(self, tagName: str, option: str): ...
    @overload
    def tag_configure(
        self,
        tagName: str,
        cnf: dict[str, Any] | None = None,
        *,
        background: str = ...,
        bgstipple: str = ...,
        borderwidth: _ScreenUnits = ...,
        border: _ScreenUnits = ...,  # alias for borderwidth
        elide: bool = ...,
        fgstipple: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        justify: Literal["left", "right", "center"] = ...,
        lmargin1: _ScreenUnits = ...,
        lmargin2: _ScreenUnits = ...,
        lmargincolor: str = ...,
        offset: _ScreenUnits = ...,
        overstrike: bool = ...,
        overstrikefg: str = ...,
        relief: _Relief = ...,
        rmargin: _ScreenUnits = ...,
        rmargincolor: str = ...,
        selectbackground: str = ...,
        selectforeground: str = ...,
        spacing1: _ScreenUnits = ...,
        spacing2: _ScreenUnits = ...,
        spacing3: _ScreenUnits = ...,
        tabs: Any = ...,  # the exact type is kind of complicated, see manual page
        tabstyle: Literal["tabular", "wordprocessor"] = ...,
        underline: bool = ...,
        underlinefg: str = ...,
        wrap: Literal["none", "char", "word"] = ...,  # be careful with "none" vs None
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def tag_configure(self, tagName: str, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    tag_config = tag_configure
    def tag_delete(self, first_tag_name: str, /, *tagNames: str) -> None: ...  # error if no tag names given
    def tag_lower(self, tagName: str, belowThis: str | None = None) -> None: ...
    def tag_names(self, index: _TextIndex | None = None) -> tuple[str, ...]: ...
    def tag_nextrange(
        self, tagName: str, index1: _TextIndex, index2: _TextIndex | None = None
    ) -> tuple[str, str] | tuple[()]: ...
    def tag_prevrange(
        self, tagName: str, index1: _TextIndex, index2: _TextIndex | None = None
    ) -> tuple[str, str] | tuple[()]: ...
    def tag_raise(self, tagName: str, aboveThis: str | None = None) -> None: ...
    def tag_ranges(self, tagName: str) -> tuple[_tkinter.Tcl_Obj, ...]: ...
    # tag_remove and tag_delete are different
    def tag_remove(self, tagName: str, index1: _TextIndex, index2: _TextIndex | None = None) -> None: ...
    @overload
    def window_cget(self, index: _TextIndex, option: Literal["padx", "pady"]) -> int: ...
    @overload
    def window_cget(self, index: _TextIndex, option: Literal["stretch"]) -> bool: ...  # actually returns Literal[0, 1]
    @overload
    def window_cget(self, index: _TextIndex, option: Literal["align"]) -> Literal["baseline", "bottom", "center", "top"]: ...
    @overload  # window is set to a widget, but read as the string name.
    def window_cget(self, index: _TextIndex, option: Literal["create", "window"]) -> str: ...
    @overload
    def window_cget(self, index: _TextIndex, option: str) -> Any: ...
    @overload
    def window_configure(self, index: _TextIndex, cnf: str) -> tuple[str, str, str, str, str | int]: ...
    @overload
    def window_configure(
        self,
        index: _TextIndex,
        cnf: dict[str, Any] | None = None,
        *,
        align: Literal["baseline", "bottom", "center", "top"] = ...,
        create: str = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        stretch: bool | Literal[0, 1] = ...,
        window: Misc | str = ...,
    ) -> dict[str, tuple[str, str, str, str, str | int]] | None: ...
    window_config = window_configure
    def window_create(
        self,
        index: _TextIndex,
        cnf: dict[str, Any] | None = {},
        *,
        align: Literal["baseline", "bottom", "center", "top"] = ...,
        create: str = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        stretch: bool | Literal[0, 1] = ...,
        window: Misc | str = ...,
    ) -> None: ...
    def window_names(self) -> tuple[str, ...]: ...
    def yview_pickplace(self, *what): ...  # deprecated

class _setit:
    def __init__(self, var, value, callback=None) -> None: ...
    def __call__(self, *args) -> None: ...

# manual page: tk_optionMenu
class OptionMenu(Menubutton):
    widgetName: Incomplete
    menuname: Incomplete
    def __init__(
        # differs from other widgets
        self,
        master: Misc | None,
        variable: StringVar,
        value: str,
        *values: str,
        # kwarg only from now on
        command: Callable[[StringVar], object] | None = ...,
    ) -> None: ...
    # configure, config, cget are inherited from Menubutton
    # destroy and __getitem__ are overridden, signature does not change

# This matches tkinter's image classes (PhotoImage and BitmapImage)
# and PIL's tkinter-compatible class (PIL.ImageTk.PhotoImage),
# but not a plain PIL image that isn't tkinter compatible.
# The reason is that PIL has width and height attributes, not methods.
@type_check_only
class _Image(Protocol):
    def width(self) -> int: ...
    def height(self) -> int: ...

@type_check_only
class _BitmapImageLike(_Image): ...

@type_check_only
class _PhotoImageLike(_Image): ...

class Image(_Image):
    name: Incomplete
    tk: _tkinter.TkappType
    def __init__(self, imgtype, name=None, cnf={}, master: Misc | _tkinter.TkappType | None = None, **kw) -> None: ...
    def __del__(self) -> None: ...
    def __setitem__(self, key, value) -> None: ...
    def __getitem__(self, key): ...
    configure: Incomplete
    config: Incomplete
    def type(self): ...

class PhotoImage(Image, _PhotoImageLike):
    # This should be kept in sync with PIL.ImageTK.PhotoImage.__init__()
    def __init__(
        self,
        name: str | None = None,
        cnf: dict[str, Any] = {},
        master: Misc | _tkinter.TkappType | None = None,
        *,
        data: str | bytes = ...,  # not same as data argument of put()
        format: str = ...,
        file: StrOrBytesPath = ...,
        gamma: float = ...,
        height: int = ...,
        palette: int | str = ...,
        width: int = ...,
    ) -> None: ...
    def configure(
        self,
        *,
        data: str | bytes = ...,
        format: str = ...,
        file: StrOrBytesPath = ...,
        gamma: float = ...,
        height: int = ...,
        palette: int | str = ...,
        width: int = ...,
    ) -> None: ...
    config = configure
    def blank(self) -> None: ...
    def cget(self, option: str) -> str: ...
    def __getitem__(self, key: str) -> str: ...  # always string: image['height'] can be '0'
    if sys.version_info >= (3, 13):
        def copy(
            self,
            *,
            from_coords: Iterable[int] | None = None,
            zoom: int | tuple[int, int] | list[int] | None = None,
            subsample: int | tuple[int, int] | list[int] | None = None,
        ) -> PhotoImage: ...
        def subsample(self, x: int, y: Literal[""] = "", *, from_coords: Iterable[int] | None = None) -> PhotoImage: ...
        def zoom(self, x: int, y: Literal[""] = "", *, from_coords: Iterable[int] | None = None) -> PhotoImage: ...
        def copy_replace(
            self,
            sourceImage: PhotoImage | str,
            *,
            from_coords: Iterable[int] | None = None,
            to: Iterable[int] | None = None,
            shrink: bool = False,
            zoom: int | tuple[int, int] | list[int] | None = None,
            subsample: int | tuple[int, int] | list[int] | None = None,
            # `None` defaults to overlay.
            compositingrule: Literal["overlay", "set"] | None = None,
        ) -> None: ...
    else:
        def copy(self) -> PhotoImage: ...
        def zoom(self, x: int, y: int | Literal[""] = "") -> PhotoImage: ...
        def subsample(self, x: int, y: int | Literal[""] = "") -> PhotoImage: ...

    def get(self, x: int, y: int) -> tuple[int, int, int]: ...
    def put(
        self,
        data: (
            str
            | bytes
            | list[str]
            | list[list[str]]
            | list[tuple[str, ...]]
            | tuple[str, ...]
            | tuple[list[str], ...]
            | tuple[tuple[str, ...], ...]
        ),
        to: tuple[int, int] | tuple[int, int, int, int] | None = None,
    ) -> None: ...
    if sys.version_info >= (3, 13):
        def read(
            self,
            filename: StrOrBytesPath,
            format: str | None = None,
            *,
            from_coords: Iterable[int] | None = None,
            to: Iterable[int] | None = None,
            shrink: bool = False,
        ) -> None: ...
        def write(
            self,
            filename: StrOrBytesPath,
            format: str | None = None,
            from_coords: Iterable[int] | None = None,
            *,
            background: str | None = None,
            grayscale: bool = False,
        ) -> None: ...
        @overload
        def data(
            self, format: str, *, from_coords: Iterable[int] | None = None, background: str | None = None, grayscale: bool = False
        ) -> bytes: ...
        @overload
        def data(
            self,
            format: None = None,
            *,
            from_coords: Iterable[int] | None = None,
            background: str | None = None,
            grayscale: bool = False,
        ) -> tuple[str, ...]: ...

    else:
        def write(
            self, filename: StrOrBytesPath, format: str | None = None, from_coords: tuple[int, int] | None = None
        ) -> None: ...

    def transparency_get(self, x: int, y: int) -> bool: ...
    def transparency_set(self, x: int, y: int, boolean: bool) -> None: ...

class BitmapImage(Image, _BitmapImageLike):
    # This should be kept in sync with PIL.ImageTK.BitmapImage.__init__()
    def __init__(
        self,
        name=None,
        cnf: dict[str, Any] = {},
        master: Misc | _tkinter.TkappType | None = None,
        *,
        background: str = ...,
        data: str | bytes = ...,
        file: StrOrBytesPath = ...,
        foreground: str = ...,
        maskdata: str = ...,
        maskfile: StrOrBytesPath = ...,
    ) -> None: ...

def image_names() -> tuple[str, ...]: ...
def image_types() -> tuple[str, ...]: ...

class Spinbox(Widget, XView):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        activebackground: str = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        buttonbackground: str = ...,
        buttoncursor: _Cursor = "",
        buttondownrelief: _Relief = ...,
        buttonuprelief: _Relief = ...,
        # percent substitutions don't seem to be supported, it's similar to Entry's validation stuff
        command: Callable[[], object] | str | list[str] | tuple[str, ...] = "",
        cursor: _Cursor = "xterm",
        disabledbackground: str = ...,
        disabledforeground: str = ...,
        exportselection: bool = True,
        fg: str = ...,
        font: _FontDescription = "TkTextFont",
        foreground: str = ...,
        format: str = "",
        from_: float = 0.0,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        increment: float = 1.0,
        insertbackground: str = ...,
        insertborderwidth: _ScreenUnits = 0,
        insertofftime: int = 300,
        insertontime: int = 600,
        insertwidth: _ScreenUnits = ...,
        invalidcommand: _EntryValidateCommand = "",
        invcmd: _EntryValidateCommand = "",
        justify: Literal["left", "center", "right"] = "left",
        name: str = ...,
        readonlybackground: str = ...,
        relief: _Relief = "sunken",
        repeatdelay: int = 400,
        repeatinterval: int = 100,
        selectbackground: str = ...,
        selectborderwidth: _ScreenUnits = ...,
        selectforeground: str = ...,
        state: Literal["normal", "disabled", "readonly"] = "normal",
        takefocus: _TakeFocusValue = "",
        textvariable: Variable = ...,
        to: float = 0.0,
        validate: Literal["none", "focus", "focusin", "focusout", "key", "all"] = "none",
        validatecommand: _EntryValidateCommand = "",
        vcmd: _EntryValidateCommand = "",
        values: list[str] | tuple[str, ...] = ...,
        width: int = 20,
        wrap: bool = False,
        xscrollcommand: _XYScrollCommand = "",
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        activebackground: str = ...,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        buttonbackground: str = ...,
        buttoncursor: _Cursor = ...,
        buttondownrelief: _Relief = ...,
        buttonuprelief: _Relief = ...,
        command: Callable[[], object] | str | list[str] | tuple[str, ...] = ...,
        cursor: _Cursor = ...,
        disabledbackground: str = ...,
        disabledforeground: str = ...,
        exportselection: bool = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        format: str = ...,
        from_: float = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        increment: float = ...,
        insertbackground: str = ...,
        insertborderwidth: _ScreenUnits = ...,
        insertofftime: int = ...,
        insertontime: int = ...,
        insertwidth: _ScreenUnits = ...,
        invalidcommand: _EntryValidateCommand = ...,
        invcmd: _EntryValidateCommand = ...,
        justify: Literal["left", "center", "right"] = ...,
        readonlybackground: str = ...,
        relief: _Relief = ...,
        repeatdelay: int = ...,
        repeatinterval: int = ...,
        selectbackground: str = ...,
        selectborderwidth: _ScreenUnits = ...,
        selectforeground: str = ...,
        state: Literal["normal", "disabled", "readonly"] = ...,
        takefocus: _TakeFocusValue = ...,
        textvariable: Variable = ...,
        to: float = ...,
        validate: Literal["none", "focus", "focusin", "focusout", "key", "all"] = ...,
        validatecommand: _EntryValidateCommand = ...,
        vcmd: _EntryValidateCommand = ...,
        values: list[str] | tuple[str, ...] = ...,
        width: int = ...,
        wrap: bool = ...,
        xscrollcommand: _XYScrollCommand = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def bbox(self, index) -> tuple[int, int, int, int] | None: ...  # type: ignore[override]
    def delete(self, first, last=None) -> Literal[""]: ...
    def get(self) -> str: ...
    def icursor(self, index): ...
    def identify(self, x: int, y: int) -> Literal["", "buttondown", "buttonup", "entry"]: ...
    def index(self, index: str | int) -> int: ...
    def insert(self, index: str | int, s: str) -> Literal[""]: ...
    # spinbox.invoke("asdf") gives error mentioning .invoke("none"), but it's not documented
    def invoke(self, element: Literal["none", "buttonup", "buttondown"]) -> Literal[""]: ...
    def scan(self, *args): ...
    def scan_mark(self, x): ...
    def scan_dragto(self, x): ...
    def selection(self, *args) -> tuple[int, ...]: ...
    def selection_adjust(self, index): ...
    def selection_clear(self): ...  # type: ignore[override]
    def selection_element(self, element=None): ...
    def selection_from(self, index: int) -> None: ...
    def selection_present(self) -> None: ...
    def selection_range(self, start: int, end: int) -> None: ...
    def selection_to(self, index: int) -> None: ...

class LabelFrame(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        background: str = ...,
        bd: _ScreenUnits = 2,
        bg: str = ...,
        border: _ScreenUnits = 2,
        borderwidth: _ScreenUnits = 2,
        class_: str = "Labelframe",  # can't be changed with configure()
        colormap: Literal["new", ""] | Misc = "",  # can't be changed with configure()
        container: bool = False,  # undocumented, can't be changed with configure()
        cursor: _Cursor = "",
        fg: str = ...,
        font: _FontDescription = "TkDefaultFont",
        foreground: str = ...,
        height: _ScreenUnits = 0,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = 0,
        # 'ne' and 'en' are valid labelanchors, but only 'ne' is a valid _Anchor.
        labelanchor: Literal["nw", "n", "ne", "en", "e", "es", "se", "s", "sw", "ws", "w", "wn"] = "nw",
        labelwidget: Misc = ...,
        name: str = ...,
        padx: _ScreenUnits = 0,
        pady: _ScreenUnits = 0,
        relief: _Relief = "groove",
        takefocus: _TakeFocusValue = 0,
        text: float | str = "",
        visual: str | tuple[str, int] = "",  # can't be changed with configure()
        width: _ScreenUnits = 0,
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = ...,
        fg: str = ...,
        font: _FontDescription = ...,
        foreground: str = ...,
        height: _ScreenUnits = ...,
        highlightbackground: str = ...,
        highlightcolor: str = ...,
        highlightthickness: _ScreenUnits = ...,
        labelanchor: Literal["nw", "n", "ne", "en", "e", "es", "se", "s", "sw", "ws", "w", "wn"] = ...,
        labelwidget: Misc = ...,
        padx: _ScreenUnits = ...,
        pady: _ScreenUnits = ...,
        relief: _Relief = ...,
        takefocus: _TakeFocusValue = ...,
        text: float | str = ...,
        width: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure

class PanedWindow(Widget):
    def __init__(
        self,
        master: Misc | None = None,
        cnf: dict[str, Any] | None = {},
        *,
        background: str = ...,
        bd: _ScreenUnits = 1,
        bg: str = ...,
        border: _ScreenUnits = 1,
        borderwidth: _ScreenUnits = 1,
        cursor: _Cursor = "",
        handlepad: _ScreenUnits = 8,
        handlesize: _ScreenUnits = 8,
        height: _ScreenUnits = "",
        name: str = ...,
        opaqueresize: bool = True,
        orient: Literal["horizontal", "vertical"] = "horizontal",
        proxybackground: str = "",
        proxyborderwidth: _ScreenUnits = 2,
        proxyrelief: _Relief = "flat",
        relief: _Relief = "flat",
        sashcursor: _Cursor = "",
        sashpad: _ScreenUnits = 0,
        sashrelief: _Relief = "flat",
        sashwidth: _ScreenUnits = 3,
        showhandle: bool = False,
        width: _ScreenUnits = "",
    ) -> None: ...
    @overload
    def configure(
        self,
        cnf: dict[str, Any] | None = None,
        *,
        background: str = ...,
        bd: _ScreenUnits = ...,
        bg: str = ...,
        border: _ScreenUnits = ...,
        borderwidth: _ScreenUnits = ...,
        cursor: _Cursor = ...,
        handlepad: _ScreenUnits = ...,
        handlesize: _ScreenUnits = ...,
        height: _ScreenUnits = ...,
        opaqueresize: bool = ...,
        orient: Literal["horizontal", "vertical"] = ...,
        proxybackground: str = ...,
        proxyborderwidth: _ScreenUnits = ...,
        proxyrelief: _Relief = ...,
        relief: _Relief = ...,
        sashcursor: _Cursor = ...,
        sashpad: _ScreenUnits = ...,
        sashrelief: _Relief = ...,
        sashwidth: _ScreenUnits = ...,
        showhandle: bool = ...,
        width: _ScreenUnits = ...,
    ) -> dict[str, tuple[str, str, str, Any, Any]] | None: ...
    @overload
    def configure(self, cnf: str) -> tuple[str, str, str, Any, Any]: ...
    config = configure
    def add(self, child: Widget, **kw) -> None: ...
    def remove(self, child) -> None: ...
    forget: Incomplete
    def identify(self, x: int, y: int): ...
    def proxy(self, *args): ...
    def proxy_coord(self): ...
    def proxy_forget(self): ...
    def proxy_place(self, x, y): ...
    def sash(self, *args): ...
    def sash_coord(self, index): ...
    def sash_mark(self, index): ...
    def sash_place(self, index, x, y): ...
    def panecget(self, child, option): ...
    def paneconfigure(self, tagOrId, cnf=None, **kw): ...
    paneconfig: Incomplete
    def panes(self): ...

def _test() -> None: ...
