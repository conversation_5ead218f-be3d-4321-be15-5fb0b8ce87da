from abc import abstractmethod
from re import Pattern
from typing_extensions import Self

class Version:
    def __eq__(self, other: object) -> bool: ...
    def __lt__(self, other: Self | str) -> bool: ...
    def __le__(self, other: Self | str) -> bool: ...
    def __gt__(self, other: Self | str) -> bool: ...
    def __ge__(self, other: Self | str) -> bool: ...
    @abstractmethod
    def __init__(self, vstring: str | None = None) -> None: ...
    @abstractmethod
    def parse(self, vstring: str) -> Self: ...
    @abstractmethod
    def __str__(self) -> str: ...
    @abstractmethod
    def _cmp(self, other: Self | str) -> bool: ...

class StrictVersion(Version):
    version_re: Pattern[str]
    version: tuple[int, int, int]
    prerelease: tuple[str, int] | None
    def __init__(self, vstring: str | None = None) -> None: ...
    def parse(self, vstring: str) -> Self: ...
    def __str__(self) -> str: ...  # noqa: Y029
    def _cmp(self, other: Self | str) -> bool: ...

class LooseVersion(Version):
    component_re: Pattern[str]
    vstring: str
    version: tuple[str | int, ...]
    def __init__(self, vstring: str | None = None) -> None: ...
    def parse(self, vstring: str) -> Self: ...
    def __str__(self) -> str: ...  # noqa: Y029
    def _cmp(self, other: Self | str) -> bool: ...
