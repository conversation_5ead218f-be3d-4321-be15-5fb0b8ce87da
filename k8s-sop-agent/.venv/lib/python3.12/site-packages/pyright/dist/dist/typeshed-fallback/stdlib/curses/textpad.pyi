from _curses import window
from collections.abc import Callable

def rectangle(win: window, uly: int, ulx: int, lry: int, lrx: int) -> None: ...

class Textbox:
    stripspaces: bool
    def __init__(self, win: window, insert_mode: bool = False) -> None: ...
    def edit(self, validate: Callable[[int], int] | None = None) -> str: ...
    def do_command(self, ch: str | int) -> None: ...
    def gather(self) -> str: ...
