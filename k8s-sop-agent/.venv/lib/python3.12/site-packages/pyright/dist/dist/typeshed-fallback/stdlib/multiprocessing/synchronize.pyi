import threading
from collections.abc import Callable
from multiprocessing.context import BaseContext
from types import TracebackType
from typing_extensions import TypeAlias

__all__ = ["Lock", "RLock", "Semaphore", "BoundedSemaphore", "Condition", "Event"]

_LockLike: TypeAlias = Lock | RLock

class Barrier(threading.Barrier):
    def __init__(
        self, parties: int, action: Callable[[], object] | None = None, timeout: float | None = None, *, ctx: BaseContext
    ) -> None: ...

class Condition:
    def __init__(self, lock: _LockLike | None = None, *, ctx: BaseContext) -> None: ...
    def notify(self, n: int = 1) -> None: ...
    def notify_all(self) -> None: ...
    def wait(self, timeout: float | None = None) -> bool: ...
    def wait_for(self, predicate: Callable[[], bool], timeout: float | None = None) -> bool: ...
    def __enter__(self) -> bool: ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None, /
    ) -> None: ...
    # These methods are copied from the lock passed to the constructor, or an
    # instance of ctx.RLock() if lock was None.
    def acquire(self, block: bool = True, timeout: float | None = None) -> bool: ...
    def release(self) -> None: ...

class Event:
    def __init__(self, *, ctx: BaseContext) -> None: ...
    def is_set(self) -> bool: ...
    def set(self) -> None: ...
    def clear(self) -> None: ...
    def wait(self, timeout: float | None = None) -> bool: ...

# Not part of public API
class SemLock:
    def __init__(self, kind: int, value: int, maxvalue: int, *, ctx: BaseContext | None) -> None: ...
    def __enter__(self) -> bool: ...
    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None, /
    ) -> None: ...
    # These methods are copied from the wrapped _multiprocessing.SemLock object
    def acquire(self, block: bool = True, timeout: float | None = None) -> bool: ...
    def release(self) -> None: ...

class Lock(SemLock):
    def __init__(self, *, ctx: BaseContext) -> None: ...

class RLock(SemLock):
    def __init__(self, *, ctx: BaseContext) -> None: ...

class Semaphore(SemLock):
    def __init__(self, value: int = 1, *, ctx: BaseContext) -> None: ...
    def get_value(self) -> int: ...

class BoundedSemaphore(Semaphore):
    def __init__(self, value: int = 1, *, ctx: BaseContext) -> None: ...
