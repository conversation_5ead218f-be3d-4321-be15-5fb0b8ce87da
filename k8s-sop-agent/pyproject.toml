[project]
name = "k8s-sop-agent"
version = "0.1.0"
description = "An AI agent for collaboratively writing expert-level Kubernetes operations SOPs."
authors = [{ name = "AI Agent", email = "<EMAIL>" }]
requires-python = ">=3.10"
dependencies = [
    # --- Core Framework ---
    "crewai>=0.30.0",
    "crewai[tools]>=0.1.0",
    # --- RAG & Vector Store ---
    "fastembed>=0.2.0",
    "qdrant-client>=1.7.0",
    "chromadb>=0.4.24", # A key dependency for crewai's internals
    # --- Core AI/LLM Libraries ---
    "langchain>=0.1.0", # crewai is built on langchain
    "pydantic>=2.0.0", # A critical dependency for almost everything
    # --- Utilities ---
    "python-dotenv>=1.0.0",
    "markdown>=3.5.0",
    "json-repair>=0.47.0",
    "tenacity>=8.0.0",
    "pybase64>=1.0.0", # Often required by embedding/ML libraries
    "numpy",
    "httpx[socks]>=0.28.1",
    "duckduckgo-search>=8.1.1",
]

[tool.uv]
dev-dependencies = [
    "pytest",
]
