#!/usr/bin/env python3
"""
Quick test to verify the memory fix is working.
This script performs a minimal test of the memory functionality.
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
load_dotenv()

def check_environment():
    """Check if environment variables are properly set."""
    print("🔍 Checking environment configuration...")
    
    required_vars = ["OPENAI_API_KEY", "OPENAI_API_BASE", "OPENAI_MODEL_NAME"]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Mask sensitive information
            if "key" in var.lower():
                masked_value = value[:10] + "..." + value[-10:] if len(value) > 20 else "***"
                print(f"  ✅ {var}: {masked_value}")
            else:
                print(f"  ✅ {var}: {value}")
        else:
            print(f"  ❌ {var}: Not set")
            return False
    
    return True

def test_session_manager():
    """Test basic session manager functionality."""
    print("\n🧪 Testing Session Manager...")
    
    try:
        from src.session_manager import SessionManager
        
        # Create session manager
        session_manager = SessionManager()
        print("  ✅ Session manager created successfully")
        
        # Create a test session
        session_id = session_manager.create_session("test")
        print(f"  ✅ Test session created: {session_id}")
        
        # List sessions
        sessions = session_manager.list_sessions()
        print(f"  ✅ Sessions listed: {len(sessions)} active sessions")
        
        # Test session switching
        session_manager.set_current_session(session_id)
        print(f"  ✅ Switched to session: {session_id}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Session manager test failed: {e}")
        return False

def test_crew_creation():
    """Test crew creation without running a full conversation."""
    print("\n🤖 Testing Crew Creation...")
    
    try:
        from src.session_manager import SessionManager
        
        session_manager = SessionManager()
        session_id = session_manager.create_session("crew_test")
        
        # Try to get crew instance
        crew = session_manager.get_current_crew()
        print("  ✅ Crew instance created successfully")
        
        # Check if crew has memory enabled
        if hasattr(crew, 'memory') and crew.memory:
            print("  ✅ Memory is enabled on crew")
        else:
            print("  ⚠️  Memory status unclear")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Crew creation test failed: {e}")
        return False

def main():
    """Run quick verification tests."""
    print("🚀 Quick Memory Fix Verification")
    print("=" * 40)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please check your .env file.")
        print("Make sure OPENAI_API_KEY and OPENAI_API_BASE are correctly set.")
        return False
    
    # Test session manager
    if not test_session_manager():
        print("\n❌ Session manager test failed.")
        return False
    
    # Test crew creation
    if not test_crew_creation():
        print("\n❌ Crew creation test failed.")
        return False
    
    print("\n✅ All quick tests passed!")
    print("\n🎯 Memory fix verification summary:")
    print("  • Environment variables are properly configured")
    print("  • Session manager is working correctly")
    print("  • Crew instances can be created successfully")
    print("  • Memory functionality should be operational")
    
    print("\n📝 Next steps:")
    print("  1. Run 'python src/main.py' to test interactive mode")
    print("  2. Run 'python demo_memory.py' for a full demonstration")
    print("  3. Run 'python test_memory.py' for comprehensive testing")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
