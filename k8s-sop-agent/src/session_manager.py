import os
import uuid
from typing import Dict, Optional
from datetime import datetime
from src.crew import K8sSopCrew


class SessionManager:
    """
    Manages conversation sessions for the K8s SOP Agent.
    Each session maintains its own crew instance and memory.
    """
    
    def __init__(self):
        self.sessions: Dict[str, Dict] = {}
        self.current_session_id: Optional[str] = None
    
    def create_session(self, session_id: Optional[str] = None) -> str:
        """
        Create a new conversation session.
        
        Args:
            session_id: Optional custom session ID. If not provided, generates a UUID.
            
        Returns:
            The session ID
        """
        if session_id is None:
            session_id = str(uuid.uuid4())[:8]  # Short UUID for readability
        
        if session_id in self.sessions:
            raise ValueError(f"Session {session_id} already exists")
        
        # Create a new crew instance for this session
        sop_crew_instance = K8sSopCrew()
        crew_instance = sop_crew_instance.crew()
        
        self.sessions[session_id] = {
            'crew_instance': crew_instance,
            'created_at': datetime.now(),
            'last_activity': datetime.now(),
            'message_count': 0
        }
        
        self.current_session_id = session_id
        return session_id
    
    def get_session(self, session_id: str) -> Dict:
        """
        Get session information.
        
        Args:
            session_id: The session ID
            
        Returns:
            Session information dictionary
        """
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")
        
        return self.sessions[session_id]
    
    def set_current_session(self, session_id: str) -> None:
        """
        Set the current active session.
        
        Args:
            session_id: The session ID to make active
        """
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")
        
        self.current_session_id = session_id
    
    def get_current_crew(self):
        """
        Get the crew instance for the current session.
        
        Returns:
            The crew instance for the current session
        """
        if self.current_session_id is None:
            raise ValueError("No active session. Create a session first.")
        
        session = self.sessions[self.current_session_id]
        session['last_activity'] = datetime.now()
        session['message_count'] += 1
        
        return session['crew_instance']
    
    def reset_session_memory(self, session_id: Optional[str] = None) -> None:
        """
        Reset memory for a specific session.
        
        Args:
            session_id: The session ID. If not provided, uses current session.
        """
        if session_id is None:
            session_id = self.current_session_id
        
        if session_id is None:
            raise ValueError("No session specified and no current session")
        
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")
        
        crew_instance = self.sessions[session_id]['crew_instance']
        try:
            crew_instance.reset_memories()
        except Exception as e:
            print(f"Warning: Could not reset memory for session {session_id}: {e}")
    
    def delete_session(self, session_id: str) -> None:
        """
        Delete a session and its associated memory.
        
        Args:
            session_id: The session ID to delete
        """
        if session_id not in self.sessions:
            raise ValueError(f"Session {session_id} not found")
        
        # Reset memory before deletion
        try:
            self.reset_session_memory(session_id)
        except Exception as e:
            print(f"Warning: Could not reset memory during session deletion: {e}")
        
        del self.sessions[session_id]
        
        # If this was the current session, clear it
        if self.current_session_id == session_id:
            self.current_session_id = None
    
    def list_sessions(self) -> Dict[str, Dict]:
        """
        List all active sessions with their metadata.
        
        Returns:
            Dictionary of session IDs and their metadata
        """
        session_info = {}
        for session_id, session_data in self.sessions.items():
            session_info[session_id] = {
                'created_at': session_data['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
                'last_activity': session_data['last_activity'].strftime('%Y-%m-%d %H:%M:%S'),
                'message_count': session_data['message_count'],
                'is_current': session_id == self.current_session_id
            }
        return session_info
    
    def cleanup_old_sessions(self, max_age_hours: int = 24) -> int:
        """
        Clean up sessions older than specified hours.
        
        Args:
            max_age_hours: Maximum age in hours before a session is considered old
            
        Returns:
            Number of sessions cleaned up
        """
        from datetime import timedelta
        
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        sessions_to_delete = []
        
        for session_id, session_data in self.sessions.items():
            if session_data['last_activity'] < cutoff_time:
                sessions_to_delete.append(session_id)
        
        for session_id in sessions_to_delete:
            self.delete_session(session_id)
        
        return len(sessions_to_delete)
