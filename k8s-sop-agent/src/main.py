import os
from dotenv import load_dotenv
from src.crew import K8sSop<PERSON>rew
from src.session_manager import SessionManager

# Load environment variables from .env file
load_dotenv()

def main():
    """
    Main function to run the K8s SOP Crew in an interactive Q&A loop with session management.
    """
    print("Initializing K8s Q&A Crew with Session Management...")
    try:
        # Initialize session manager
        session_manager = SessionManager()

        # Create default session
        default_session_id = session_manager.create_session("default")
        print(f"Created default session: {default_session_id}")

        print("Crew initialized successfully. You can now ask questions.")
        print("Available commands:")
        print("  'exit' or 'quit' - Stop the application")
        print("  'reset' - Clear current session memory")
        print("  'new session [id]' - Create new session")
        print("  'switch session <id>' - Switch to existing session")
        print("  'list sessions' - Show all sessions")
        print("  'delete session <id>' - Delete a session")
        print("-" * 50)

        while True:
            try:
                current_session = session_manager.current_session_id
                question = input(f"[{current_session}] Your question: ")

                if question.lower() in ["exit", "quit"]:
                    print("Exiting application. Goodbye!")
                    break

                # Handle session commands
                if question.lower() == "reset":
                    print("Resetting current session memory...")
                    try:
                        session_manager.reset_session_memory()
                        print("Memory reset successfully.")
                    except Exception as e:
                        print(f"Warning: Could not reset memory: {e}")
                    continue

                if question.lower().startswith("new session"):
                    parts = question.split()
                    session_id = parts[2] if len(parts) > 2 else None
                    try:
                        new_session_id = session_manager.create_session(session_id)
                        print(f"Created and switched to new session: {new_session_id}")
                    except ValueError as e:
                        print(f"Error: {e}")
                    continue

                if question.lower().startswith("switch session"):
                    parts = question.split()
                    if len(parts) > 2:
                        session_id = parts[2]
                        try:
                            session_manager.set_current_session(session_id)
                            print(f"Switched to session: {session_id}")
                        except ValueError as e:
                            print(f"Error: {e}")
                    else:
                        print("Usage: switch session <session_id>")
                    continue

                if question.lower() == "list sessions":
                    sessions = session_manager.list_sessions()
                    if sessions:
                        print("\nActive sessions:")
                        for session_id, info in sessions.items():
                            current_marker = " (current)" if info['is_current'] else ""
                            print(f"  {session_id}: {info['message_count']} messages, "
                                  f"last activity: {info['last_activity']}{current_marker}")
                    else:
                        print("No active sessions.")
                    continue

                if question.lower().startswith("delete session"):
                    parts = question.split()
                    if len(parts) > 2:
                        session_id = parts[2]
                        try:
                            session_manager.delete_session(session_id)
                            print(f"Deleted session: {session_id}")
                        except ValueError as e:
                            print(f"Error: {e}")
                    else:
                        print("Usage: delete session <session_id>")
                    continue

                if not question.strip():
                    continue

                # Use the current session's crew instance to maintain memory
                try:
                    crew_instance = session_manager.get_current_crew()
                    result = crew_instance.kickoff(inputs={'question': question})

                    print("\n" + "="*50)
                    print("Final Answer:")
                    print(result)
                    print("="*50 + "\n")
                except ValueError as e:
                    print(f"Session error: {e}")

            except KeyboardInterrupt:
                print("\nExiting application. Goodbye!")
                break
            except Exception as e:
                print(f"\nAn error occurred: {e}")
                print("Please try again or type 'exit' to quit.")

    except Exception as e:
        print(f"Failed to initialize the crew: {e}")
        print("Please check your configuration and environment variables.")

if __name__ == "__main__":
    main()