import os
import json
import requests
from typing import Any, Dict, List, Optional, Union
from crewai import BaseLLM

class CustomLLM(BaseLLM):
    """
    A custom LLM class to interact with a specific OpenAI-compatible API endpoint.
    This class ensures that only supported parameters are sent in the request payload,
    avoiding issues with endpoints that do not support all OpenAI features (like tools).
    """
    def __init__(self, model: str, api_key: str, endpoint: str, temperature: float = 0.7):
        super().__init__(model=model, temperature=temperature)
        self.api_key = api_key
        self.endpoint = endpoint

    def call(
        self,
        messages: Union[str, List[Dict[str, str]]],
        tools: Optional[List[dict]] = None,
        callbacks: Optional[List[Any]] = None,
        available_functions: Optional[Dict[str, Any]] = None,
    ) -> Union[str, Any]:
        """
        Makes a call to the custom LLM API.
        """
        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]

        # Construct a minimal payload to ensure compatibility.
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": self.temperature,
            "stream": True  # Enable streaming
        }

        try:
            response = requests.post(
                self.endpoint,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json=payload,
                timeout=120,
                stream=True  # Process as a stream
            )
            response.raise_for_status()
            
            # Read the raw content first to allow for debugging
            raw_content = response.content
            
            full_content = ""
            # Process the raw content line by line
            for line in raw_content.splitlines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith('data: '):
                        json_str = decoded_line[len('data: '):]
                        if json_str.strip() == '[DONE]':
                            break
                        try:
                            chunk = json.loads(json_str)
                            if chunk['choices'][0]['delta'].get('content'):
                                full_content += chunk['choices'][0]['delta']['content']
                        except json.JSONDecodeError:
                            print(f"Could not decode JSON from line: {json_str}")

            return full_content

        except requests.Timeout:
            raise TimeoutError("LLM request timed out.")
        except requests.RequestException as e:
            print(f"LLM request failed. Response text: {response.text}")
            raise RuntimeError(f"LLM request failed: {e}")
        except (KeyError, IndexError, json.JSONDecodeError) as e:
            raise ValueError(f"Invalid response format from LLM: {e}")

    def supports_function_calling(self) -> bool:
        """
        Explicitly disable function calling to prevent unsupported parameters
        from being added to the request.
        """
        return False