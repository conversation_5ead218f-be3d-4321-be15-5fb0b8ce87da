import os
from crewai.tools import BaseTool
from qdrant_client import Qdrant<PERSON><PERSON>
from langchain_community.embeddings import FastEmbedEmbeddings
from langchain_community.vectorstores import Qdrant
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

class KnowledgeBaseTool(BaseTool):
    name: str = "KnowledgeBaseTool"
    description: str = "A tool to query the Kubernetes SOP knowledge base. Use it to get information on K8s best practices or to understand the context of a user's SOP."

    _client: QdrantClient = None
    _embedding_function = None
    _text_splitter = None
    _static_kb_path: str = "src/kb/static"
    _dynamic_kb_path: str = "src/kb/dynamic"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._client = QdrantClient(":memory:")
        self._embedding_function = FastEmbedEmbeddings()
        self._text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=100)
        self._build_kb()

    def _build_kb(self):
        """Builds the knowledge base from static and dynamic sources."""
        print("Building knowledge base...")
        self._load_and_ingest(self._static_kb_path, "static_kb")
        self._load_and_ingest(self._dynamic_kb_path, "dynamic_kb")
        print("Knowledge base built successfully.")

    def _load_and_ingest(self, path: str, collection_name: str):
        """Loads documents and ingests them into a Qdrant collection."""
        # Ensure dummy files exist for initialization
        if not os.path.exists(path):
            os.makedirs(path)

        # Create a dummy file if the directory is empty
        if not any(os.scandir(path)):
            with open(os.path.join(path, "placeholder.md"), "w") as f:
                f.write("This is a placeholder document.")

        # Using a simple loop instead of DirectoryLoader to avoid extra dependencies
        docs = []
        for filename in os.listdir(path):
            if filename.endswith(".md"):
                loader = TextLoader(os.path.join(path, filename))
                docs.extend(loader.load())

        if not docs:
            print(f"No documents found in {path} for collection {collection_name}.")
            return

        split_docs = self._text_splitter.split_documents(docs)

        # Use the correct parameter name for Qdrant.from_documents
        Qdrant.from_documents(
            split_docs,
            self._embedding_function,
            location=":memory:",
            collection_name=collection_name,
            force_recreate=True,
        )
        print(f"Successfully ingested {len(split_docs)} chunks into '{collection_name}'.")

    def _run(self, query: str, collection_name: str = "static_kb") -> str:
        """
        Queries a specific collection in the knowledge base.
        Use 'static_kb' for general Kubernetes knowledge and 'dynamic_kb' for user-specific SOP context.
        """
        vector_store = Qdrant(
            location=":memory:",
            collection_name=collection_name,
            embeddings=self._embedding_function,
        )
        retriever = vector_store.as_retriever()
        results = retriever.get_relevant_documents(query)
        return "\n".join([doc.page_content for doc in results])
