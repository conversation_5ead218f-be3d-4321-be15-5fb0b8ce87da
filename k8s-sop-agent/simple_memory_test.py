#!/usr/bin/env python3
"""
Simple memory test to verify the fix is working.
This script performs a basic test without complex interactions.
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.session_manager import SessionManager

# Load environment variables
load_dotenv()

def simple_memory_test():
    """Perform a simple memory test."""
    print("🧪 Simple Memory Test")
    print("=" * 30)
    
    try:
        # Create session manager
        session_manager = SessionManager()
        session_id = session_manager.create_session("simple_test")
        print(f"✅ Created session: {session_id}")
        
        # Get crew instance
        crew = session_manager.get_current_crew()
        print("✅ Got crew instance")
        
        # Test 1: Simple question
        print("\n📝 Test 1: Simple question")
        question1 = "What is a pod in Kubernetes?"
        print(f"Question: {question1}")
        
        try:
            result1 = crew.kickoff(inputs={'question': question1})
            print("✅ First question answered successfully")
            print(f"Answer preview: {str(result1)[:100]}...")
        except Exception as e:
            print(f"❌ First question failed: {e}")
            return False
        
        # Test 2: Reference previous context
        print("\n📝 Test 2: Reference previous context")
        question2 = "Can you elaborate on what we just discussed?"
        print(f"Question: {question2}")
        
        try:
            result2 = crew.kickoff(inputs={'question': question2})
            print("✅ Second question answered successfully")
            print(f"Answer preview: {str(result2)[:100]}...")
        except Exception as e:
            print(f"❌ Second question failed: {e}")
            return False
        
        print("\n🎉 Simple memory test completed successfully!")
        print("The system can maintain context between questions.")
        return True
        
    except Exception as e:
        print(f"❌ Simple memory test failed: {e}")
        return False

def main():
    """Run the simple memory test."""
    print("🚀 Running Simple Memory Test")
    print("This test verifies basic memory functionality.")
    print()
    
    # Check environment
    required_vars = ["OPENAI_API_KEY", "OPENAI_API_BASE", "OPENAI_MODEL_NAME"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    # Run test
    success = simple_memory_test()
    
    if success:
        print("\n✅ Memory functionality is working correctly!")
        print("You can now use the interactive mode with confidence.")
    else:
        print("\n❌ Memory test failed. Please check the configuration.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
