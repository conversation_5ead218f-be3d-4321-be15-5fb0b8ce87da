#!/usr/bin/env python3
"""
Demo script to showcase multi-turn conversation memory functionality.
This script demonstrates how the system remembers context across multiple interactions.
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.session_manager import SessionManager

# Load environment variables
load_dotenv()

def demo_conversation_memory():
    """Demonstrate conversation memory with a realistic scenario."""
    print("🤖 K8s SOP Agent - Memory Demo")
    print("=" * 50)
    
    # Check environment variables
    required_vars = ["OPENAI_API_KEY", "OPENAI_API_BASE", "OPENAI_MODEL_NAME"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {missing_vars}")
        print("Please set them in your .env file before running the demo.")
        return False
    
    try:
        # Initialize session manager
        session_manager = SessionManager()
        session_id = session_manager.create_session("demo")
        print(f"📝 Created demo session: {session_id}")
        print()
        
        # Conversation sequence to demonstrate memory
        conversation = [
            {
                "question": "I'm having trouble with my Kubernetes deployment. The pods keep crashing.",
                "context": "Establishing initial problem context"
            },
            {
                "question": "The error message mentions 'ImagePullBackOff'. What does this mean?",
                "context": "Adding specific error details"
            },
            {
                "question": "How can I fix the issue we just discussed?",
                "context": "Referencing previous context - should remember ImagePullBackOff"
            },
            {
                "question": "What was the original problem I mentioned?",
                "context": "Testing memory of initial context"
            }
        ]
        
        crew = session_manager.get_current_crew()
        
        for i, interaction in enumerate(conversation, 1):
            print(f"🔄 Interaction {i}: {interaction['context']}")
            print(f"❓ Question: {interaction['question']}")
            print("🤔 Thinking...")
            
            try:
                result = crew.kickoff(inputs={'question': interaction['question']})
                print(f"💡 Answer: {str(result)[:300]}...")
                print()
                print("-" * 50)
                print()
                
                # Pause for readability
                input("Press Enter to continue to next interaction...")
                print()
                
            except Exception as e:
                print(f"❌ Error in interaction {i}: {e}")
                return False
        
        print("✅ Demo completed successfully!")
        print("🎯 Key observations:")
        print("   • The agent should remember the initial problem (pod crashes)")
        print("   • It should recall the specific error (ImagePullBackOff)")
        print("   • Later questions should reference previous context")
        print("   • Memory persists across the entire conversation")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

def demo_session_switching():
    """Demonstrate session isolation and switching."""
    print("\n🔄 Session Switching Demo")
    print("=" * 30)
    
    try:
        session_manager = SessionManager()
        
        # Create two sessions with different contexts
        print("📝 Creating two separate sessions...")
        
        # Session 1: Development environment
        dev_session = session_manager.create_session("development")
        crew_dev = session_manager.get_current_crew()
        crew_dev.kickoff(inputs={'question': "I'm working on a development Kubernetes cluster with 3 nodes."})
        print(f"✅ Session '{dev_session}': Established development context")
        
        # Session 2: Production environment
        prod_session = session_manager.create_session("production")
        session_manager.set_current_session(prod_session)
        crew_prod = session_manager.get_current_crew()
        crew_prod.kickoff(inputs={'question': "I'm managing a production Kubernetes cluster with 10 nodes and strict security policies."})
        print(f"✅ Session '{prod_session}': Established production context")
        
        # Test memory isolation
        print("\n🧪 Testing memory isolation...")
        
        # Switch back to dev session
        session_manager.set_current_session(dev_session)
        crew_dev_again = session_manager.get_current_crew()
        dev_result = crew_dev_again.kickoff(inputs={'question': "How many nodes does my cluster have?"})
        print(f"🔧 Dev session memory: {str(dev_result)[:200]}...")
        
        # Switch to prod session
        session_manager.set_current_session(prod_session)
        crew_prod_again = session_manager.get_current_crew()
        prod_result = crew_prod_again.kickoff(inputs={'question': "How many nodes does my cluster have?"})
        print(f"🏭 Prod session memory: {str(prod_result)[:200]}...")
        
        print("\n✅ Session switching demo completed!")
        print("🎯 Each session should remember its own context independently")
        
        return True
        
    except Exception as e:
        print(f"❌ Session switching demo failed: {e}")
        return False

def main():
    """Run the memory demonstration."""
    print("🚀 Starting K8s SOP Agent Memory Demonstration")
    print("This demo shows how the agent maintains conversation memory")
    print("across multiple interactions and sessions.")
    print()
    
    # Run conversation memory demo
    if not demo_conversation_memory():
        return False
    
    # Ask if user wants to see session switching demo
    print()
    response = input("Would you like to see the session switching demo? (y/n): ")
    if response.lower().startswith('y'):
        if not demo_session_switching():
            return False
    
    print("\n🎉 All demos completed successfully!")
    print("The memory functionality is working as expected.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
