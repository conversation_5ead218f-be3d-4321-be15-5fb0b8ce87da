#!/usr/bin/env python3
"""
Test script for verifying multi-turn conversation memory functionality.
This script tests the memory persistence across multiple interactions.
"""

import os
import sys
import time
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.session_manager import SessionManager

# Load environment variables
load_dotenv()

def test_basic_memory():
    """Test basic memory functionality within a single session."""
    print("=== Testing Basic Memory Functionality ===")
    
    try:
        # Initialize session manager
        session_manager = SessionManager()
        session_id = session_manager.create_session("test_basic")
        
        print(f"Created test session: {session_id}")
        
        # First interaction - establish context
        print("\n1. First interaction - establishing context...")
        crew = session_manager.get_current_crew()
        
        first_question = "What is Kubernetes?"
        print(f"Question: {first_question}")
        
        result1 = crew.kickoff(inputs={'question': first_question})
        print(f"Answer: {str(result1)[:200]}...")
        
        # Second interaction - reference previous context
        print("\n2. Second interaction - referencing previous context...")
        second_question = "Can you elaborate on what we just discussed?"
        print(f"Question: {second_question}")
        
        result2 = crew.kickoff(inputs={'question': second_question})
        print(f"Answer: {str(result2)[:200]}...")
        
        # Third interaction - test memory retention
        print("\n3. Third interaction - testing memory retention...")
        third_question = "What was my first question?"
        print(f"Question: {third_question}")
        
        result3 = crew.kickoff(inputs={'question': third_question})
        print(f"Answer: {str(result3)[:200]}...")
        
        print("\n✅ Basic memory test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Basic memory test failed: {e}")
        return False

def test_session_isolation():
    """Test memory isolation between different sessions."""
    print("\n=== Testing Session Isolation ===")
    
    try:
        session_manager = SessionManager()
        
        # Create first session
        session1_id = session_manager.create_session("test_session1")
        print(f"Created session 1: {session1_id}")
        
        # First session interaction
        crew1 = session_manager.get_current_crew()
        result1 = crew1.kickoff(inputs={'question': "My name is Alice and I work with Docker containers."})
        print("Session 1: Established context about Alice and Docker")
        
        # Create second session
        session2_id = session_manager.create_session("test_session2")
        session_manager.set_current_session(session2_id)
        print(f"Created and switched to session 2: {session2_id}")
        
        # Second session interaction
        crew2 = session_manager.get_current_crew()
        result2 = crew2.kickoff(inputs={'question': "My name is Bob and I work with Kubernetes pods."})
        print("Session 2: Established context about Bob and Kubernetes")
        
        # Test isolation - switch back to session 1
        session_manager.set_current_session(session1_id)
        crew1_again = session_manager.get_current_crew()
        result3 = crew1_again.kickoff(inputs={'question': "What is my name and what do I work with?"})
        print(f"Session 1 memory test: {str(result3)[:200]}...")
        
        # Test isolation - switch to session 2
        session_manager.set_current_session(session2_id)
        crew2_again = session_manager.get_current_crew()
        result4 = crew2_again.kickoff(inputs={'question': "What is my name and what do I work with?"})
        print(f"Session 2 memory test: {str(result4)[:200]}...")
        
        print("\n✅ Session isolation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Session isolation test failed: {e}")
        return False

def test_memory_reset():
    """Test memory reset functionality."""
    print("\n=== Testing Memory Reset ===")
    
    try:
        session_manager = SessionManager()
        session_id = session_manager.create_session("test_reset")
        
        # Establish some context
        crew = session_manager.get_current_crew()
        crew.kickoff(inputs={'question': "Remember that I am a DevOps engineer working on microservices."})
        print("Established context about DevOps engineer")
        
        # Verify memory exists
        result1 = crew.kickoff(inputs={'question': "What do you know about me?"})
        print(f"Before reset: {str(result1)[:200]}...")
        
        # Reset memory
        print("Resetting session memory...")
        session_manager.reset_session_memory()
        
        # Test if memory was cleared
        crew_after_reset = session_manager.get_current_crew()
        result2 = crew_after_reset.kickoff(inputs={'question': "What do you know about me?"})
        print(f"After reset: {str(result2)[:200]}...")
        
        print("\n✅ Memory reset test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Memory reset test failed: {e}")
        return False

def test_session_management():
    """Test session management operations."""
    print("\n=== Testing Session Management ===")
    
    try:
        session_manager = SessionManager()
        
        # Create multiple sessions
        session1 = session_manager.create_session("mgmt_test1")
        session2 = session_manager.create_session("mgmt_test2")
        session3 = session_manager.create_session("mgmt_test3")
        
        print(f"Created sessions: {session1}, {session2}, {session3}")
        
        # Test listing sessions
        sessions = session_manager.list_sessions()
        print(f"Listed {len(sessions)} sessions")
        
        # Test switching sessions
        session_manager.set_current_session(session2)
        assert session_manager.current_session_id == session2
        print(f"Successfully switched to session: {session2}")
        
        # Test deleting a session
        session_manager.delete_session(session3)
        sessions_after_delete = session_manager.list_sessions()
        assert session3 not in sessions_after_delete
        print(f"Successfully deleted session: {session3}")
        
        print("\n✅ Session management test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Session management test failed: {e}")
        return False

def main():
    """Run all memory tests."""
    print("Starting Memory Functionality Tests")
    print("=" * 50)
    
    # Check if required environment variables are set
    required_vars = ["OPENAI_API_KEY", "OPENAI_API_BASE", "OPENAI_MODEL_NAME"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {missing_vars}")
        print("Please set them in your .env file before running tests.")
        return False
    
    tests = [
        test_session_management,
        test_basic_memory,
        test_session_isolation,
        test_memory_reset,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            time.sleep(2)  # Brief pause between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Memory functionality is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
